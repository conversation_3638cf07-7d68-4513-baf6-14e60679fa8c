"""Tooling engine that manages and executes all available tools."""

import logging
from typing import Any, Dict, List, Optional

from ai_cli.config.models import Config
from ai_cli.tools.file_ops import FileOperationsTool
from ai_cli.tools.shell import ShellTool
from ai_cli.tools.web_search import WebSearchTool
from ai_cli.utils.exceptions import ToolExecutionError

logger = logging.getLogger(__name__)


class ToolingEngine:
    """Central engine for managing and executing tools."""
    
    def __init__(self, config: Config) -> None:
        """Initialize tooling engine with available tools.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.tools: Dict[str, Any] = {}
        
        # Initialize all available tools
        self._init_tools()
        
        logger.info(f"Tooling engine initialized with {len(self.tools)} tools")
    
    def _init_tools(self) -> None:
        """Initialize all available tools."""
        try:
            # Shell command execution tool
            self.tools["run_shell_command"] = ShellTool(self.config)
            
            # File operations tools
            file_ops = FileOperationsTool(self.config)
            self.tools["read_file"] = file_ops
            self.tools["write_file"] = file_ops
            self.tools["list_directory"] = file_ops
            self.tools["create_directory"] = file_ops
            self.tools["delete_file"] = file_ops
            self.tools["delete_directory"] = file_ops
            self.tools["move_file"] = file_ops
            self.tools["copy_file"] = file_ops
            self.tools["file_exists"] = file_ops
            self.tools["get_file_info"] = file_ops
            
            # Web search tool
            if self.config.web_search.enabled:
                web_search = WebSearchTool(self.config)
                self.tools["search_web"] = web_search
                self.tools["fetch_webpage"] = web_search
            
            logger.info("All tools initialized successfully")
            
        except Exception as e:
            logger.error(f"Error initializing tools: {e}")
    
    async def execute_tool(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        session_id: str,
    ) -> Dict[str, Any]:
        """Execute a specific tool with given arguments.
        
        Args:
            tool_name: Name of the tool to execute
            arguments: Arguments to pass to the tool
            session_id: Session identifier for context
            
        Returns:
            Tool execution result
        """
        if tool_name not in self.tools:
            raise ToolExecutionError(f"Unknown tool: {tool_name}")
        
        tool = self.tools[tool_name]
        
        try:
            logger.info(f"Executing tool: {tool_name} with args: {arguments}")
            
            # Execute the tool based on its type
            if tool_name == "run_shell_command":
                result = await tool.execute_command(
                    command=arguments.get("command", ""),
                    working_directory=arguments.get("working_directory"),
                    timeout=arguments.get("timeout", 30),
                    session_id=session_id,
                )
            
            elif tool_name == "read_file":
                result = await tool.read_file(
                    path=arguments.get("path", ""),
                    encoding=arguments.get("encoding", "utf-8"),
                    max_size=arguments.get("max_size"),
                )
            
            elif tool_name == "write_file":
                result = await tool.write_file(
                    path=arguments.get("path", ""),
                    content=arguments.get("content", ""),
                    encoding=arguments.get("encoding", "utf-8"),
                    append=arguments.get("append", False),
                )
            
            elif tool_name == "list_directory":
                result = await tool.list_directory(
                    path=arguments.get("path", "."),
                    pattern=arguments.get("pattern"),
                    recursive=arguments.get("recursive", False),
                    include_hidden=arguments.get("include_hidden", False),
                )
            
            elif tool_name == "create_directory":
                result = await tool.create_directory(
                    path=arguments.get("path", ""),
                    parents=arguments.get("parents", True),
                )
            
            elif tool_name == "delete_file":
                result = await tool.delete_file(
                    path=arguments.get("path", ""),
                )
            
            elif tool_name == "delete_directory":
                result = await tool.delete_directory(
                    path=arguments.get("path", ""),
                    recursive=arguments.get("recursive", False),
                )
            
            elif tool_name == "move_file":
                result = await tool.move_file(
                    source=arguments.get("source", ""),
                    destination=arguments.get("destination", ""),
                )
            
            elif tool_name == "copy_file":
                result = await tool.copy_file(
                    source=arguments.get("source", ""),
                    destination=arguments.get("destination", ""),
                )
            
            elif tool_name == "file_exists":
                result = await tool.file_exists(
                    path=arguments.get("path", ""),
                )
            
            elif tool_name == "get_file_info":
                result = await tool.get_file_info(
                    path=arguments.get("path", ""),
                )
            
            elif tool_name == "search_web":
                result = await tool.search_web(
                    query=arguments.get("query", ""),
                    num_results=arguments.get("num_results", 5),
                )
            
            elif tool_name == "fetch_webpage":
                result = await tool.fetch_webpage(
                    url=arguments.get("url", ""),
                    timeout=arguments.get("timeout", 10),
                )
            
            else:
                raise ToolExecutionError(f"Tool execution not implemented: {tool_name}")
            
            logger.info(f"Tool {tool_name} executed successfully")
            return result
            
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "tool": tool_name,
                "arguments": arguments,
            }
    
    def get_tool_schemas(self) -> List[Dict[str, Any]]:
        """Get OpenAI function calling schemas for all available tools.
        
        Returns:
            List of tool schemas in OpenAI format
        """
        schemas = []
        
        # Shell command tool
        schemas.append({
            "type": "function",
            "function": {
                "name": "run_shell_command",
                "description": "Execute a shell command on the system. Use with caution.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "command": {
                            "type": "string",
                            "description": "The shell command to execute"
                        },
                        "working_directory": {
                            "type": "string",
                            "description": "Optional working directory for the command"
                        },
                        "timeout": {
                            "type": "integer",
                            "description": "Command timeout in seconds (default: 30)"
                        }
                    },
                    "required": ["command"]
                }
            }
        })
        
        # File operations
        file_schemas = [
            {
                "name": "read_file",
                "description": "Read the contents of a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to the file to read"},
                        "encoding": {"type": "string", "description": "File encoding (default: utf-8)"},
                        "max_size": {"type": "integer", "description": "Maximum file size to read in bytes"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "write_file",
                "description": "Write content to a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to the file to write"},
                        "content": {"type": "string", "description": "Content to write to the file"},
                        "encoding": {"type": "string", "description": "File encoding (default: utf-8)"},
                        "append": {"type": "boolean", "description": "Whether to append to the file (default: false)"}
                    },
                    "required": ["path", "content"]
                }
            },
            {
                "name": "list_directory",
                "description": "List contents of a directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Directory path to list (default: current directory)"},
                        "pattern": {"type": "string", "description": "Optional glob pattern to filter files"},
                        "recursive": {"type": "boolean", "description": "Whether to list recursively (default: false)"},
                        "include_hidden": {"type": "boolean", "description": "Whether to include hidden files (default: false)"}
                    },
                    "required": []
                }
            },
            {
                "name": "create_directory",
                "description": "Create a new directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path of the directory to create"},
                        "parents": {"type": "boolean", "description": "Create parent directories if needed (default: true)"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "delete_file",
                "description": "Delete a file",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to the file to delete"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "file_exists",
                "description": "Check if a file or directory exists",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to check"}
                    },
                    "required": ["path"]
                }
            },
            {
                "name": "get_file_info",
                "description": "Get information about a file or directory",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "path": {"type": "string", "description": "Path to get information about"}
                    },
                    "required": ["path"]
                }
            }
        ]
        
        for schema in file_schemas:
            schemas.append({"type": "function", "function": schema})
        
        # Web search tools
        if self.config.web_search.enabled:
            web_schemas = [
                {
                    "name": "search_web",
                    "description": "Search the web for information",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "query": {"type": "string", "description": "Search query"},
                            "num_results": {"type": "integer", "description": "Number of results to return (default: 5)"}
                        },
                        "required": ["query"]
                    }
                },
                {
                    "name": "fetch_webpage",
                    "description": "Fetch and extract content from a webpage",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "url": {"type": "string", "description": "URL of the webpage to fetch"},
                            "timeout": {"type": "integer", "description": "Request timeout in seconds (default: 10)"}
                        },
                        "required": ["url"]
                    }
                }
            ]
            
            for schema in web_schemas:
                schemas.append({"type": "function", "function": schema})
        
        return schemas
    
    def get_available_tools(self) -> List[str]:
        """Get list of available tool names.
        
        Returns:
            List of tool names
        """
        return list(self.tools.keys())
    
    async def cleanup(self) -> None:
        """Clean up tooling engine resources."""
        logger.info("Cleaning up tooling engine")
        
        for tool in self.tools.values():
            if hasattr(tool, 'cleanup'):
                await tool.cleanup()
