"""Custom exceptions for AI CLI Terminal Tool."""


class AICliError(Exception):
    """Base exception for AI CLI Terminal Tool."""
    pass


class ConfigurationError(AICliError):
    """Raised when there's a configuration error."""
    pass


class LLMError(AICliError):
    """Raised when there's an error with LLM operations."""
    pass


class ToolExecutionError(AICliError):
    """Raised when there's an error executing a tool."""
    pass


class CommandExecutionError(ToolExecutionError):
    """Raised when there's an error executing a shell command."""
    pass


class FileOperationError(ToolExecutionError):
    """Raised when there's an error with file operations."""
    pass


class WebSearchError(ToolExecutionError):
    """Raised when there's an error with web search operations."""
    pass


class SessionError(AICliError):
    """Raised when there's an error with session management."""
    pass


class ContextError(AICliError):
    """Raised when there's an error with context management."""
    pass
