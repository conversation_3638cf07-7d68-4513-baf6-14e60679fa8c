"""Context manager for automatic environment awareness."""

import fnmatch
import logging
import os
from pathlib import Path
from typing import Any, Dict, List, Optional, Set

try:
    import git
    HAS_GIT = True
except ImportError:
    HAS_GIT = False

from ai_cli.config.models import Config

logger = logging.getLogger(__name__)


class ContextManager:
    """Manages automatic context gathering from the user's environment."""
    
    def __init__(self, config: Config) -> None:
        """Initialize context manager.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.explicit_context: Set[str] = set()
        
        logger.info("Context manager initialized")
    
    async def gather_context(self, user_query: str = "") -> Dict[str, Any]:
        """Gather relevant context from the environment.
        
        Args:
            user_query: User query to help determine relevant context
            
        Returns:
            Dictionary containing context information
        """
        context = {}
        
        if not self.config.context.auto_context:
            return context
        
        try:
            # Current directory information
            context["current_directory"] = str(Path.cwd())
            
            # Directory listing
            context["directory_listing"] = await self._get_directory_listing()
            
            # Git information
            if self.config.context.include_git_info:
                context["git_info"] = await self._get_git_info()
            
            # Relevant files based on query
            context["files"] = await self._get_relevant_files(user_query)
            
            # Environment variables (limited set)
            context["environment"] = self._get_relevant_env_vars()
            
            # Explicit context items
            if self.explicit_context:
                context["explicit_context"] = await self._get_explicit_context()
            
            logger.debug(f"Gathered context with {len(context)} categories")
            
        except Exception as e:
            logger.error(f"Error gathering context: {e}")
        
        return context
    
    async def _get_directory_listing(self) -> List[Dict[str, Any]]:
        """Get listing of current directory.
        
        Returns:
            List of directory items
        """
        try:
            current_dir = Path.cwd()
            items = []
            
            for item in current_dir.iterdir():
                # Skip hidden files unless explicitly included
                if item.name.startswith('.') and not self._should_include_hidden(item.name):
                    continue
                
                # Skip excluded patterns
                if self._is_excluded(item.name):
                    continue
                
                try:
                    stat = item.stat()
                    items.append({
                        "name": item.name,
                        "type": "directory" if item.is_dir() else "file",
                        "size": stat.st_size if item.is_file() else None,
                        "modified": stat.st_mtime,
                    })
                except (OSError, PermissionError):
                    continue
            
            # Sort: directories first, then files
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            return items[:50]  # Limit to first 50 items
            
        except Exception as e:
            logger.error(f"Error getting directory listing: {e}")
            return []
    
    async def _get_git_info(self) -> Optional[Dict[str, Any]]:
        """Get Git repository information.
        
        Returns:
            Git information dictionary or None
        """
        if not HAS_GIT:
            return None
        
        try:
            repo = git.Repo(search_parent_directories=True)
            
            # Get current branch
            try:
                branch = repo.active_branch.name
            except TypeError:
                # Detached HEAD
                branch = str(repo.head.commit)[:8]
            
            # Get status
            status_info = []
            if repo.is_dirty():
                status_info.append("dirty")
            
            # Get untracked files
            untracked = repo.untracked_files
            if untracked:
                status_info.append(f"{len(untracked)} untracked")
            
            # Get uncommitted changes
            uncommitted = []
            for item in repo.index.diff(None):
                uncommitted.append(item.a_path)
            for item in repo.index.diff("HEAD"):
                uncommitted.append(item.a_path)
            
            return {
                "branch": branch,
                "status": ", ".join(status_info) if status_info else "clean",
                "uncommitted_changes": len(set(uncommitted)),
                "untracked_files": len(untracked),
                "repo_root": str(repo.working_dir),
            }
            
        except (git.InvalidGitRepositoryError, git.GitCommandError):
            return None
        except Exception as e:
            logger.error(f"Error getting Git info: {e}")
            return None
    
    async def _get_relevant_files(self, user_query: str) -> List[Dict[str, Any]]:
        """Get files relevant to the user query.
        
        Args:
            user_query: User query to determine relevance
            
        Returns:
            List of relevant file information
        """
        try:
            current_dir = Path.cwd()
            relevant_files = []
            
            # Keywords from query to help determine relevance
            query_lower = user_query.lower()
            keywords = set(query_lower.split())
            
            # File extensions that might be relevant
            code_extensions = {'.py', '.js', '.ts', '.java', '.cpp', '.c', '.h', '.cs', '.go', '.rs', '.rb', '.php'}
            config_extensions = {'.json', '.yaml', '.yml', '.toml', '.ini', '.cfg', '.conf'}
            doc_extensions = {'.md', '.txt', '.rst', '.adoc'}
            
            # Scan current directory and immediate subdirectories
            for root, dirs, files in os.walk(current_dir):
                root_path = Path(root)
                
                # Skip deep nesting
                if len(root_path.parts) - len(current_dir.parts) > 2:
                    continue
                
                # Skip excluded directories
                dirs[:] = [d for d in dirs if not self._is_excluded(d)]
                
                for file in files:
                    if self._is_excluded(file):
                        continue
                    
                    file_path = root_path / file
                    file_ext = file_path.suffix.lower()
                    
                    # Determine relevance score
                    relevance = 0
                    
                    # File name matches keywords
                    if any(keyword in file.lower() for keyword in keywords):
                        relevance += 3
                    
                    # File extension relevance
                    if file_ext in code_extensions:
                        relevance += 2
                    elif file_ext in config_extensions:
                        relevance += 1
                    elif file_ext in doc_extensions:
                        relevance += 1
                    
                    # Special files
                    special_files = {'readme', 'license', 'changelog', 'makefile', 'dockerfile'}
                    if file.lower().split('.')[0] in special_files:
                        relevance += 2
                    
                    # Only include files with some relevance
                    if relevance > 0:
                        try:
                            stat = file_path.stat()
                            
                            # Skip very large files
                            if stat.st_size > self.config.context.max_file_size:
                                continue
                            
                            relevant_files.append({
                                "path": str(file_path.relative_to(current_dir)),
                                "type": "file",
                                "size": stat.st_size,
                                "extension": file_ext,
                                "relevance": relevance,
                                "modified": stat.st_mtime,
                            })
                            
                        except (OSError, PermissionError):
                            continue
            
            # Sort by relevance and limit
            relevant_files.sort(key=lambda x: x["relevance"], reverse=True)
            return relevant_files[:self.config.context.max_context_files]
            
        except Exception as e:
            logger.error(f"Error getting relevant files: {e}")
            return []
    
    def _get_relevant_env_vars(self) -> Dict[str, str]:
        """Get relevant environment variables.
        
        Returns:
            Dictionary of relevant environment variables
        """
        relevant_vars = ["PATH", "HOME", "USER", "PWD", "SHELL", "TERM"]
        env_vars = {}
        
        for var in relevant_vars:
            value = os.environ.get(var)
            if value:
                env_vars[var] = value
        
        return env_vars
    
    async def _get_explicit_context(self) -> List[Dict[str, Any]]:
        """Get explicitly added context items.
        
        Returns:
            List of explicit context items
        """
        context_items = []
        
        for path_str in self.explicit_context:
            try:
                path = Path(path_str)
                if path.exists():
                    stat = path.stat()
                    context_items.append({
                        "path": str(path),
                        "type": "directory" if path.is_dir() else "file",
                        "size": stat.st_size if path.is_file() else None,
                        "explicit": True,
                    })
            except Exception as e:
                logger.error(f"Error processing explicit context {path_str}: {e}")
        
        return context_items
    
    def _is_excluded(self, name: str) -> bool:
        """Check if a file/directory should be excluded.
        
        Args:
            name: File or directory name
            
        Returns:
            True if should be excluded
        """
        for pattern in self.config.context.exclude_patterns:
            if fnmatch.fnmatch(name, pattern):
                return True
        return False
    
    def _should_include_hidden(self, name: str) -> bool:
        """Check if a hidden file should be included.
        
        Args:
            name: File name
            
        Returns:
            True if should be included
        """
        # Include important hidden files
        important_hidden = {'.gitignore', '.env', '.dockerignore', '.editorconfig'}
        return name in important_hidden
    
    async def add_explicit_context(self, path: str) -> None:
        """Add a path to explicit context.
        
        Args:
            path: Path to add to context
        """
        resolved_path = str(Path(path).resolve())
        self.explicit_context.add(resolved_path)
        logger.info(f"Added explicit context: {resolved_path}")
    
    def remove_explicit_context(self, path: str) -> None:
        """Remove a path from explicit context.
        
        Args:
            path: Path to remove from context
        """
        resolved_path = str(Path(path).resolve())
        self.explicit_context.discard(resolved_path)
        logger.info(f"Removed explicit context: {resolved_path}")
    
    def clear_explicit_context(self) -> None:
        """Clear all explicit context."""
        self.explicit_context.clear()
        logger.info("Cleared all explicit context")
