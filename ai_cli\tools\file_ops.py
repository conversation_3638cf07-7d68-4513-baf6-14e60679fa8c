"""File operations tool for reading, writing, and manipulating files."""

import asyncio
import fnmatch
import logging
import shutil
from pathlib import Path
from typing import Any, Dict, List, Optional

from ai_cli.config.models import Config
from ai_cli.utils.exceptions import FileOperationError

logger = logging.getLogger(__name__)


class FileOperationsTool:
    """Tool for file system operations."""
    
    def __init__(self, config: Config) -> None:
        """Initialize file operations tool.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.max_file_size = config.context.max_file_size
        
        logger.info("File operations tool initialized")
    
    async def read_file(
        self,
        path: str,
        encoding: str = "utf-8",
        max_size: Optional[int] = None,
    ) -> Dict[str, Any]:
        """Read contents of a file.
        
        Args:
            path: Path to the file
            encoding: File encoding
            max_size: Maximum file size to read
            
        Returns:
            Dictionary containing file contents and metadata
        """
        try:
            file_path = Path(path).resolve()
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File does not exist: {path}",
                    "content": "",
                    "path": str(file_path),
                }
            
            if not file_path.is_file():
                return {
                    "success": False,
                    "error": f"Path is not a file: {path}",
                    "content": "",
                    "path": str(file_path),
                }
            
            # Check file size
            file_size = file_path.stat().st_size
            size_limit = max_size or self.max_file_size
            
            if file_size > size_limit:
                return {
                    "success": False,
                    "error": f"File too large: {file_size} bytes (limit: {size_limit})",
                    "content": "",
                    "path": str(file_path),
                    "size": file_size,
                }
            
            # Read file content
            content = await asyncio.to_thread(
                file_path.read_text, encoding=encoding
            )
            
            logger.info(f"Read file: {file_path} ({file_size} bytes)")
            
            return {
                "success": True,
                "content": content,
                "path": str(file_path),
                "size": file_size,
                "encoding": encoding,
            }
            
        except UnicodeDecodeError as e:
            logger.error(f"Encoding error reading {path}: {e}")
            return {
                "success": False,
                "error": f"Encoding error: {str(e)}. Try a different encoding.",
                "content": "",
                "path": path,
            }
        except Exception as e:
            logger.error(f"Error reading file {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to read file: {str(e)}",
                "content": "",
                "path": path,
            }
    
    async def write_file(
        self,
        path: str,
        content: str,
        encoding: str = "utf-8",
        append: bool = False,
    ) -> Dict[str, Any]:
        """Write content to a file.
        
        Args:
            path: Path to the file
            content: Content to write
            encoding: File encoding
            append: Whether to append to existing file
            
        Returns:
            Dictionary containing operation result
        """
        try:
            file_path = Path(path).resolve()
            
            # Create parent directories if needed
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Write content
            if append:
                await asyncio.to_thread(
                    file_path.write_text, 
                    file_path.read_text(encoding=encoding) + content,
                    encoding=encoding
                )
                operation = "appended to"
            else:
                await asyncio.to_thread(
                    file_path.write_text, content, encoding=encoding
                )
                operation = "written to"
            
            file_size = file_path.stat().st_size
            
            logger.info(f"Content {operation} file: {file_path} ({file_size} bytes)")
            
            return {
                "success": True,
                "path": str(file_path),
                "size": file_size,
                "operation": operation,
                "encoding": encoding,
            }
            
        except Exception as e:
            logger.error(f"Error writing to file {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to write file: {str(e)}",
                "path": path,
            }
    
    async def list_directory(
        self,
        path: str = ".",
        pattern: Optional[str] = None,
        recursive: bool = False,
        include_hidden: bool = False,
    ) -> Dict[str, Any]:
        """List contents of a directory.
        
        Args:
            path: Directory path
            pattern: Optional glob pattern to filter files
            recursive: Whether to list recursively
            include_hidden: Whether to include hidden files
            
        Returns:
            Dictionary containing directory listing
        """
        try:
            dir_path = Path(path).resolve()
            
            if not dir_path.exists():
                return {
                    "success": False,
                    "error": f"Directory does not exist: {path}",
                    "items": [],
                    "path": str(dir_path),
                }
            
            if not dir_path.is_dir():
                return {
                    "success": False,
                    "error": f"Path is not a directory: {path}",
                    "items": [],
                    "path": str(dir_path),
                }
            
            items = []
            
            if recursive:
                iterator = dir_path.rglob("*")
            else:
                iterator = dir_path.iterdir()
            
            for item in iterator:
                # Skip hidden files if not requested
                if not include_hidden and item.name.startswith('.'):
                    continue
                
                # Apply pattern filter
                if pattern and not fnmatch.fnmatch(item.name, pattern):
                    continue
                
                try:
                    stat = item.stat()
                    item_info = {
                        "name": item.name,
                        "path": str(item),
                        "type": "directory" if item.is_dir() else "file",
                        "size": stat.st_size if item.is_file() else None,
                        "modified": stat.st_mtime,
                        "permissions": oct(stat.st_mode)[-3:],
                    }
                    
                    # Add file extension for files
                    if item.is_file():
                        item_info["extension"] = item.suffix
                    
                    items.append(item_info)
                    
                except (OSError, PermissionError):
                    # Skip items we can't access
                    continue
            
            # Sort items: directories first, then files, both alphabetically
            items.sort(key=lambda x: (x["type"] != "directory", x["name"].lower()))
            
            logger.info(f"Listed directory: {dir_path} ({len(items)} items)")
            
            return {
                "success": True,
                "items": items,
                "path": str(dir_path),
                "count": len(items),
                "pattern": pattern,
                "recursive": recursive,
            }
            
        except Exception as e:
            logger.error(f"Error listing directory {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to list directory: {str(e)}",
                "items": [],
                "path": path,
            }
    
    async def create_directory(
        self,
        path: str,
        parents: bool = True,
    ) -> Dict[str, Any]:
        """Create a directory.
        
        Args:
            path: Directory path to create
            parents: Whether to create parent directories
            
        Returns:
            Dictionary containing operation result
        """
        try:
            dir_path = Path(path).resolve()
            
            if dir_path.exists():
                if dir_path.is_dir():
                    return {
                        "success": True,
                        "path": str(dir_path),
                        "message": "Directory already exists",
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Path exists but is not a directory: {path}",
                        "path": str(dir_path),
                    }
            
            dir_path.mkdir(parents=parents, exist_ok=True)
            
            logger.info(f"Created directory: {dir_path}")
            
            return {
                "success": True,
                "path": str(dir_path),
                "message": "Directory created successfully",
            }
            
        except Exception as e:
            logger.error(f"Error creating directory {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to create directory: {str(e)}",
                "path": path,
            }
    
    async def delete_file(self, path: str) -> Dict[str, Any]:
        """Delete a file.
        
        Args:
            path: Path to the file to delete
            
        Returns:
            Dictionary containing operation result
        """
        try:
            file_path = Path(path).resolve()
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"File does not exist: {path}",
                    "path": str(file_path),
                }
            
            if not file_path.is_file():
                return {
                    "success": False,
                    "error": f"Path is not a file: {path}",
                    "path": str(file_path),
                }
            
            file_path.unlink()
            
            logger.info(f"Deleted file: {file_path}")
            
            return {
                "success": True,
                "path": str(file_path),
                "message": "File deleted successfully",
            }
            
        except Exception as e:
            logger.error(f"Error deleting file {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to delete file: {str(e)}",
                "path": path,
            }
    
    async def delete_directory(
        self,
        path: str,
        recursive: bool = False,
    ) -> Dict[str, Any]:
        """Delete a directory.
        
        Args:
            path: Path to the directory to delete
            recursive: Whether to delete recursively
            
        Returns:
            Dictionary containing operation result
        """
        try:
            dir_path = Path(path).resolve()
            
            if not dir_path.exists():
                return {
                    "success": False,
                    "error": f"Directory does not exist: {path}",
                    "path": str(dir_path),
                }
            
            if not dir_path.is_dir():
                return {
                    "success": False,
                    "error": f"Path is not a directory: {path}",
                    "path": str(dir_path),
                }
            
            if recursive:
                shutil.rmtree(dir_path)
            else:
                dir_path.rmdir()  # Only works if directory is empty
            
            logger.info(f"Deleted directory: {dir_path}")
            
            return {
                "success": True,
                "path": str(dir_path),
                "message": "Directory deleted successfully",
            }
            
        except OSError as e:
            if "not empty" in str(e).lower():
                return {
                    "success": False,
                    "error": "Directory not empty. Use recursive=true to delete non-empty directories.",
                    "path": path,
                }
            else:
                logger.error(f"Error deleting directory {path}: {e}")
                return {
                    "success": False,
                    "error": f"Failed to delete directory: {str(e)}",
                    "path": path,
                }
        except Exception as e:
            logger.error(f"Error deleting directory {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to delete directory: {str(e)}",
                "path": path,
            }
    
    async def move_file(self, source: str, destination: str) -> Dict[str, Any]:
        """Move a file or directory.
        
        Args:
            source: Source path
            destination: Destination path
            
        Returns:
            Dictionary containing operation result
        """
        try:
            src_path = Path(source).resolve()
            dst_path = Path(destination).resolve()
            
            if not src_path.exists():
                return {
                    "success": False,
                    "error": f"Source does not exist: {source}",
                    "source": str(src_path),
                    "destination": str(dst_path),
                }
            
            # Create destination parent directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            shutil.move(str(src_path), str(dst_path))
            
            logger.info(f"Moved: {src_path} -> {dst_path}")
            
            return {
                "success": True,
                "source": str(src_path),
                "destination": str(dst_path),
                "message": "File moved successfully",
            }
            
        except Exception as e:
            logger.error(f"Error moving {source} to {destination}: {e}")
            return {
                "success": False,
                "error": f"Failed to move file: {str(e)}",
                "source": source,
                "destination": destination,
            }
    
    async def copy_file(self, source: str, destination: str) -> Dict[str, Any]:
        """Copy a file or directory.
        
        Args:
            source: Source path
            destination: Destination path
            
        Returns:
            Dictionary containing operation result
        """
        try:
            src_path = Path(source).resolve()
            dst_path = Path(destination).resolve()
            
            if not src_path.exists():
                return {
                    "success": False,
                    "error": f"Source does not exist: {source}",
                    "source": str(src_path),
                    "destination": str(dst_path),
                }
            
            # Create destination parent directory if needed
            dst_path.parent.mkdir(parents=True, exist_ok=True)
            
            if src_path.is_file():
                shutil.copy2(str(src_path), str(dst_path))
            else:
                shutil.copytree(str(src_path), str(dst_path))
            
            logger.info(f"Copied: {src_path} -> {dst_path}")
            
            return {
                "success": True,
                "source": str(src_path),
                "destination": str(dst_path),
                "message": "File copied successfully",
            }
            
        except Exception as e:
            logger.error(f"Error copying {source} to {destination}: {e}")
            return {
                "success": False,
                "error": f"Failed to copy file: {str(e)}",
                "source": source,
                "destination": destination,
            }
    
    async def file_exists(self, path: str) -> Dict[str, Any]:
        """Check if a file or directory exists.
        
        Args:
            path: Path to check
            
        Returns:
            Dictionary containing existence check result
        """
        try:
            file_path = Path(path).resolve()
            exists = file_path.exists()
            
            result = {
                "success": True,
                "exists": exists,
                "path": str(file_path),
            }
            
            if exists:
                result["type"] = "directory" if file_path.is_dir() else "file"
            
            return result
            
        except Exception as e:
            logger.error(f"Error checking existence of {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to check file existence: {str(e)}",
                "path": path,
                "exists": False,
            }
    
    async def get_file_info(self, path: str) -> Dict[str, Any]:
        """Get detailed information about a file or directory.
        
        Args:
            path: Path to get information about
            
        Returns:
            Dictionary containing file information
        """
        try:
            file_path = Path(path).resolve()
            
            if not file_path.exists():
                return {
                    "success": False,
                    "error": f"Path does not exist: {path}",
                    "path": str(file_path),
                }
            
            stat = file_path.stat()
            
            info = {
                "success": True,
                "path": str(file_path),
                "name": file_path.name,
                "type": "directory" if file_path.is_dir() else "file",
                "size": stat.st_size,
                "modified": stat.st_mtime,
                "created": stat.st_ctime,
                "permissions": oct(stat.st_mode)[-3:],
            }
            
            if file_path.is_file():
                info["extension"] = file_path.suffix
                info["stem"] = file_path.stem
            
            return info
            
        except Exception as e:
            logger.error(f"Error getting info for {path}: {e}")
            return {
                "success": False,
                "error": f"Failed to get file info: {str(e)}",
                "path": path,
            }
