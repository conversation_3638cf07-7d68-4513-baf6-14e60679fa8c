"""Tests for tool implementations."""

import pytest
import tempfile
from pathlib import Path

from ai_cli.config.models import Config
from ai_cli.tools.file_ops import FileOperationsTool


@pytest.fixture
def config():
    """Create test configuration."""
    return Config()


@pytest.fixture
def file_ops_tool(config):
    """Create file operations tool."""
    return FileOperationsTool(config)


@pytest.mark.asyncio
async def test_file_read_write(file_ops_tool):
    """Test file read and write operations."""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "test.txt"
        test_content = "Hello, World!\nThis is a test file."
        
        # Write file
        result = await file_ops_tool.write_file(
            path=str(test_file),
            content=test_content
        )
        
        assert result["success"] is True
        assert test_file.exists()
        
        # Read file
        result = await file_ops_tool.read_file(path=str(test_file))
        
        assert result["success"] is True
        assert result["content"] == test_content


@pytest.mark.asyncio
async def test_file_exists(file_ops_tool):
    """Test file existence check."""
    with tempfile.TemporaryDirectory() as temp_dir:
        existing_file = Path(temp_dir) / "existing.txt"
        existing_file.write_text("content")
        
        non_existing_file = Path(temp_dir) / "non_existing.txt"
        
        # Test existing file
        result = await file_ops_tool.file_exists(str(existing_file))
        assert result["success"] is True
        assert result["exists"] is True
        assert result["type"] == "file"
        
        # Test non-existing file
        result = await file_ops_tool.file_exists(str(non_existing_file))
        assert result["success"] is True
        assert result["exists"] is False


@pytest.mark.asyncio
async def test_directory_operations(file_ops_tool):
    """Test directory operations."""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_dir = Path(temp_dir) / "test_directory"
        
        # Create directory
        result = await file_ops_tool.create_directory(str(test_dir))
        assert result["success"] is True
        assert test_dir.exists()
        assert test_dir.is_dir()
        
        # List directory (should be empty)
        result = await file_ops_tool.list_directory(str(test_dir))
        assert result["success"] is True
        assert len(result["items"]) == 0
        
        # Create some files in the directory
        (test_dir / "file1.txt").write_text("content1")
        (test_dir / "file2.py").write_text("print('hello')")
        
        # List directory again
        result = await file_ops_tool.list_directory(str(test_dir))
        assert result["success"] is True
        assert len(result["items"]) == 2
        
        # Check file names
        file_names = [item["name"] for item in result["items"]]
        assert "file1.txt" in file_names
        assert "file2.py" in file_names


@pytest.mark.asyncio
async def test_file_copy_move(file_ops_tool):
    """Test file copy and move operations."""
    with tempfile.TemporaryDirectory() as temp_dir:
        source_file = Path(temp_dir) / "source.txt"
        copy_file = Path(temp_dir) / "copy.txt"
        move_file = Path(temp_dir) / "moved.txt"
        
        # Create source file
        test_content = "Test content for copy/move"
        source_file.write_text(test_content)
        
        # Copy file
        result = await file_ops_tool.copy_file(
            source=str(source_file),
            destination=str(copy_file)
        )
        assert result["success"] is True
        assert copy_file.exists()
        assert copy_file.read_text() == test_content
        assert source_file.exists()  # Original should still exist
        
        # Move file
        result = await file_ops_tool.move_file(
            source=str(source_file),
            destination=str(move_file)
        )
        assert result["success"] is True
        assert move_file.exists()
        assert move_file.read_text() == test_content
        assert not source_file.exists()  # Original should be gone


@pytest.mark.asyncio
async def test_file_delete(file_ops_tool):
    """Test file deletion."""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "to_delete.txt"
        test_file.write_text("This file will be deleted")
        
        assert test_file.exists()
        
        # Delete file
        result = await file_ops_tool.delete_file(str(test_file))
        assert result["success"] is True
        assert not test_file.exists()


@pytest.mark.asyncio
async def test_get_file_info(file_ops_tool):
    """Test getting file information."""
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = Path(temp_dir) / "info_test.py"
        test_content = "print('Hello, World!')"
        test_file.write_text(test_content)
        
        result = await file_ops_tool.get_file_info(str(test_file))
        
        assert result["success"] is True
        assert result["name"] == "info_test.py"
        assert result["type"] == "file"
        assert result["extension"] == ".py"
        assert result["stem"] == "info_test"
        assert result["size"] == len(test_content)


if __name__ == "__main__":
    pytest.main([__file__])
