"""AI Core that manages LLM interactions and function calling."""

import json
import logging
from typing import Any, Dict, List, Optional

from ai_cli.config.models import Config
from ai_cli.llm.providers.base import BaseLLMProvider, LLMMessage, LLMResponse
from ai_cli.llm.providers.deepseek import DeepseekProvider
from ai_cli.llm.providers.ollama import OllamaProvider
from ai_cli.llm.prompts import PromptManager
from ai_cli.tools.engine import ToolingEngine
from ai_cli.utils.exceptions import LLMError

logger = logging.getLogger(__name__)


class AICore:
    """Core AI component that manages LLM interactions and autonomous task execution."""
    
    def __init__(self, config: Config, tooling_engine: ToolingEngine) -> None:
        """Initialize AI Core.
        
        Args:
            config: Application configuration
            tooling_engine: Tool execution engine
        """
        self.config = config
        self.tooling_engine = tooling_engine
        self.prompt_manager = PromptManager(config)
        
        # Initialize LLM providers
        self.providers: Dict[str, BaseLLMProvider] = {}
        self._init_providers()
        
        # Current provider
        self.current_provider: Optional[BaseLLMProvider] = None
        self._set_default_provider()
        
        logger.info(f"AI Core initialized with provider: {config.llm.default_provider}")
    
    def _init_providers(self) -> None:
        """Initialize all available LLM providers."""
        # Initialize Deepseek provider
        if self.config.llm.deepseek.get("api_key"):
            try:
                self.providers["deepseek"] = DeepseekProvider(self.config.llm.deepseek)
                logger.info("Deepseek provider initialized")
            except Exception as e:
                logger.warning(f"Failed to initialize Deepseek provider: {e}")
        
        # Initialize Ollama provider
        try:
            self.providers["ollama"] = OllamaProvider(self.config.llm.ollama)
            logger.info("Ollama provider initialized")
        except Exception as e:
            logger.warning(f"Failed to initialize Ollama provider: {e}")
    
    async def _set_default_provider(self) -> None:
        """Set the default provider based on configuration and availability."""
        preferred = self.config.llm.default_provider
        
        if preferred in self.providers:
            provider = self.providers[preferred]
            if await provider.is_available():
                self.current_provider = provider
                logger.info(f"Using {preferred} provider")
                return
        
        # Fallback to any available provider
        for name, provider in self.providers.items():
            if await provider.is_available():
                self.current_provider = provider
                logger.info(f"Falling back to {name} provider")
                return
        
        logger.error("No LLM providers available")
    
    async def process_query(
        self,
        query: str,
        context: Dict[str, Any],
        history: List[Dict[str, Any]],
        session_id: str,
    ) -> Dict[str, Any]:
        """Process user query with autonomous task execution.
        
        Args:
            query: User query string
            context: Current context information
            history: Conversation history
            session_id: Session identifier
            
        Returns:
            Response dictionary
        """
        if not self.current_provider:
            return {
                "type": "error",
                "content": "No LLM provider available. Please check your configuration."
            }
        
        try:
            # Build messages for LLM
            messages = self.prompt_manager.build_messages(query, context, history)
            
            # Get available tools
            tools = self.tooling_engine.get_tool_schemas()
            
            # Initial LLM call
            response = await self.current_provider.generate_response(
                messages=messages,
                tools=tools,
                temperature=0.7,
            )
            
            # Handle autonomous task execution
            if response.tool_calls:
                return await self._handle_autonomous_execution(
                    messages, response, tools, session_id
                )
            else:
                return {
                    "type": "response",
                    "content": response.content,
                    "provider": self.current_provider.get_model_info()["provider"]
                }
                
        except Exception as e:
            logger.error(f"Error processing query: {e}")
            return {
                "type": "error",
                "content": f"Error processing your request: {str(e)}"
            }
    
    async def _handle_autonomous_execution(
        self,
        initial_messages: List[LLMMessage],
        initial_response: LLMResponse,
        tools: List[Dict[str, Any]],
        session_id: str,
        max_iterations: int = 10,
    ) -> Dict[str, Any]:
        """Handle autonomous task execution with function calling.
        
        Args:
            initial_messages: Initial conversation messages
            initial_response: Initial LLM response with tool calls
            tools: Available tools
            session_id: Session identifier
            max_iterations: Maximum number of iterations to prevent infinite loops
            
        Returns:
            Final response dictionary
        """
        messages = initial_messages.copy()
        current_response = initial_response
        execution_log = []
        iteration = 0
        
        # Add initial assistant message
        if current_response.content:
            messages.append(LLMMessage(
                role="assistant",
                content=current_response.content,
                tool_calls=current_response.tool_calls
            ))
        
        while current_response.tool_calls and iteration < max_iterations:
            iteration += 1
            logger.info(f"Autonomous execution iteration {iteration}")
            
            # Execute all tool calls
            for tool_call in current_response.tool_calls:
                try:
                    # Extract tool information
                    function_name = tool_call["function"]["name"]
                    arguments = json.loads(tool_call["function"]["arguments"])
                    
                    logger.info(f"Executing tool: {function_name} with args: {arguments}")
                    
                    # Execute the tool
                    result = await self.tooling_engine.execute_tool(
                        function_name, arguments, session_id
                    )
                    
                    # Add tool result to messages
                    messages.append(LLMMessage(
                        role="tool",
                        content=json.dumps(result),
                        tool_call_id=tool_call["id"]
                    ))
                    
                    # Log execution
                    execution_log.append({
                        "tool": function_name,
                        "arguments": arguments,
                        "result": result,
                        "iteration": iteration
                    })
                    
                except Exception as e:
                    logger.error(f"Error executing tool {tool_call}: {e}")
                    
                    # Add error result to messages
                    error_result = {"error": str(e), "success": False}
                    messages.append(LLMMessage(
                        "tool",
                        json.dumps(error_result),
                        tool_call_id=tool_call["id"]
                    ))
                    
                    execution_log.append({
                        "tool": function_name,
                        "arguments": arguments,
                        "error": str(e),
                        "iteration": iteration
                    })
            
            # Get next LLM response
            try:
                current_response = await self.current_provider.generate_response(
                    messages=messages,
                    tools=tools,
                    temperature=0.7,
                )
                
                # Add assistant response to messages if it has content
                if current_response.content:
                    messages.append(LLMMessage(
                        role="assistant",
                        content=current_response.content,
                        tool_calls=current_response.tool_calls
                    ))
                
            except Exception as e:
                logger.error(f"Error in autonomous execution iteration {iteration}: {e}")
                break
        
        # Prepare final response
        final_content = current_response.content if current_response.content else "Task completed."
        
        # Add execution summary if tools were used
        if execution_log:
            summary = self._create_execution_summary(execution_log)
            final_content = f"{final_content}\n\n**Execution Summary:**\n{summary}"
        
        return {
            "type": "autonomous_response",
            "content": final_content,
            "execution_log": execution_log,
            "iterations": iteration,
            "provider": self.current_provider.get_model_info()["provider"]
        }
    
    def _create_execution_summary(self, execution_log: List[Dict[str, Any]]) -> str:
        """Create a summary of autonomous execution.
        
        Args:
            execution_log: List of executed tools and results
            
        Returns:
            Formatted execution summary
        """
        summary_lines = []
        
        for entry in execution_log:
            tool = entry["tool"]
            iteration = entry["iteration"]
            
            if "error" in entry:
                summary_lines.append(f"- Step {iteration}: {tool} - ❌ Error: {entry['error']}")
            else:
                summary_lines.append(f"- Step {iteration}: {tool} - ✅ Success")
        
        return "\n".join(summary_lines)
    
    async def switch_provider(self, provider_name: str) -> bool:
        """Switch to a different LLM provider.
        
        Args:
            provider_name: Name of the provider to switch to
            
        Returns:
            True if switch was successful, False otherwise
        """
        if provider_name not in self.providers:
            logger.error(f"Provider {provider_name} not available")
            return False
        
        provider = self.providers[provider_name]
        if not await provider.is_available():
            logger.error(f"Provider {provider_name} is not available")
            return False
        
        self.current_provider = provider
        logger.info(f"Switched to {provider_name} provider")
        return True
    
    def get_current_provider_info(self) -> Optional[Dict[str, Any]]:
        """Get information about the current provider.
        
        Returns:
            Provider information or None if no provider is set
        """
        if self.current_provider:
            return self.current_provider.get_model_info()
        return None
    
    async def cleanup(self) -> None:
        """Clean up AI Core resources."""
        logger.info("Cleaning up AI Core")
        
        for provider in self.providers.values():
            await provider.cleanup()
