# 🤖 AI-Powered CLI Terminal Tool

An intelligent command-line interface that integrates Large Language Models (LLMs) like Deepseek and Ollama to provide autonomous task execution, natural language understanding, and enhanced developer productivity.

## ✨ Features

- 🤖 **AI Integration**: Support for both local (Ollama) and cloud-based (Deepseek) LLMs
- 🔧 **Autonomous Task Execution**: LLM can decompose complex requests into executable steps
- 🖥️ **Cross-Platform Shell Execution**: Works on Windows (WSL), macOS, and Linux
- 📁 **Intelligent File Operations**: Context-aware file system operations
- 🌐 **Web Search Integration**: Built-in web search capabilities
- 📊 **Rich CLI Interface**: Beautiful terminal UI with animations and diff views
- 💾 **Session Management**: Persistent conversation history and context
- 🎯 **Context Awareness**: Automatic detection of current environment and relevant files
- 🔍 **Diff Viewer**: Built-in text comparison with syntax highlighting
- 🛡️ **Security Features**: Command confirmation and safety checks
- 🎨 **Customizable UI**: Configurable animations, themes, and display options

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- (Optional) [Ollama](https://ollama.ai) for local LLM support
- (Optional) Deepseek API key for cloud LLM support

### Installation

#### Option 1: Automated Installation (Recommended)

```bash
# Clone the repository
git clone <repository-url>
cd ai-cli-terminal

# Run the installation script
python install.py
```

#### Option 2: Manual Installation

```bash
# Clone the repository
git clone <repository-url>
cd ai-cli-terminal

# Install with Poetry (recommended)
poetry install

# Or with pip
pip install -e .

# Initialize configuration
ai-cli init-config
```

#### Option 3: Using Make

```bash
# Install and setup development environment
make dev-setup

# Or just install
make install
```

### Configuration

The tool will automatically create a configuration file at `~/.config/ai_cli_tool/config.yaml`. Edit this file to customize your settings:

```yaml
# ~/.config/ai_cli_tool/config.yaml
llm:
  default_provider: "ollama"  # or "deepseek"
  deepseek:
    api_key: "your-deepseek-api-key"  # Add your API key here
    model: "deepseek-chat"
  ollama:
    base_url: "http://localhost:11434"
    model: "llama2"  # or any other Ollama model

ui:
  enable_animation: true
  confirm_shell_commands: true
  syntax_highlighting: true
  show_timestamps: true

security:
  require_command_confirmation: true
  blocked_commands:
    - "rm -rf /"
    - "sudo rm"
    - "format"
```

### First Run

```bash
# Start the AI CLI tool
ai-cli

# Or with debug mode
ai-cli --debug

# Or using make
make run
```

## 📖 Usage Examples

### Basic Commands

```bash
# File operations
> list all python files in the current directory
> read the contents of main.py
> create a new file called hello.py with a simple hello world function

# Shell commands
> check the git status of this repository
> run the test suite
> install the requests library using pip

# Web search
> search for "python async best practices" and summarize the top 3 results
> find information about FastAPI and create a simple example

# Complex tasks
> refactor all Python files in the src directory to add type hints
> set up a new Python project with pytest, black, and pre-commit hooks
> analyze the code in this directory and suggest improvements
```

### Internal Commands

```bash
# Session management
/session start my-project
/session list
/session switch <session-id>
/session end

# Context management
/context add src/
/context remove old_file.py
/context list
/context clear

# Utility commands
/help
/history
/config
/clear
/exit
```

### Advanced Usage

The AI can autonomously execute complex multi-step tasks:

```bash
> Create a FastAPI application with the following requirements:
  1. A user model with SQLAlchemy
  2. CRUD endpoints for users
  3. Authentication with JWT tokens
  4. Docker configuration
  5. Unit tests with pytest
  6. Documentation with proper docstrings
```

The AI will break this down into steps and execute them automatically, asking for confirmation when needed.

## Architecture

The tool follows a modular architecture with these key components:

- **CLI Interface**: Rich terminal UI with animations and interactive prompts
- **Orchestration Layer**: Central coordinator managing data flow between components
- **AI Core**: LLM integration with function calling and autonomous planning
- **Tooling Engine**: Execution of shell commands, file operations, and web searches
- **Context Manager**: Automatic environment awareness and context gathering
- **Session Manager**: Persistent conversation history and state management

## Development

### Setup Development Environment

```bash
# Install development dependencies
poetry install --with dev

# Run tests
poetry run pytest

# Format code
poetry run black .
poetry run ruff check --fix .

# Type checking
poetry run mypy ai_cli/
```

### Project Structure

```
ai_cli/
├── core/           # Core business logic
├── ui/             # CLI interface and UI components
├── llm/            # LLM integration and abstractions
├── tools/          # Tool implementations (shell, file, web)
├── context/        # Context management
├── session/        # Session management
├── config/         # Configuration management
└── utils/          # Utility functions
```

## Security Considerations

- **Command Execution**: By default, all LLM-generated shell commands require user confirmation
- **API Keys**: Store securely in configuration files with restricted permissions
- **Data Privacy**: Local LLMs (Ollama) keep data local; cloud LLMs send data to providers
- **Sandboxing**: Consider running in containers for additional isolation

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.
