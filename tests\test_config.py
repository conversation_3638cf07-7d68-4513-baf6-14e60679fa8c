"""Tests for configuration management."""

import pytest
from pathlib import Path
import tempfile
import yaml

from ai_cli.config.manager import ConfigManager
from ai_cli.config.models import Config


def test_default_config_creation():
    """Test creation of default configuration."""
    config = Config()
    
    assert config.llm.default_provider == "ollama"
    assert config.ui.enable_animation is True
    assert config.context.auto_context is True
    assert config.security.require_command_confirmation is True


def test_config_manager_load_default():
    """Test loading default configuration when no file exists."""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        assert isinstance(config, Config)
        assert config.llm.default_provider == "ollama"


def test_config_manager_save_and_load():
    """Test saving and loading configuration."""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = Path(temp_dir) / "config.yaml"
        
        # Create and save config
        config_manager = ConfigManager(config_path)
        original_config = Config()
        original_config.llm.default_provider = "deepseek"
        original_config.ui.enable_animation = False
        
        config_manager.save_config(original_config, config_path)
        
        # Load config
        loaded_config = config_manager.load_config()
        
        assert loaded_config.llm.default_provider == "deepseek"
        assert loaded_config.ui.enable_animation is False


def test_config_init():
    """Test configuration initialization."""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = Path(temp_dir) / "config.yaml"
        
        config_manager = ConfigManager()
        created_path = config_manager.init_config(config_path)
        
        assert created_path == config_path
        assert config_path.exists()
        
        # Verify content
        with open(config_path, 'r') as f:
            data = yaml.safe_load(f)
        
        assert "llm" in data
        assert "ui" in data
        assert "context" in data


def test_config_init_force_overwrite():
    """Test configuration initialization with force overwrite."""
    with tempfile.TemporaryDirectory() as temp_dir:
        config_path = Path(temp_dir) / "config.yaml"
        
        # Create initial file
        config_path.write_text("existing content")
        
        config_manager = ConfigManager()
        
        # Should fail without force
        with pytest.raises(FileExistsError):
            config_manager.init_config(config_path, force=False)
        
        # Should succeed with force
        created_path = config_manager.init_config(config_path, force=True)
        assert created_path == config_path
        
        # Verify it's a proper config file now
        with open(config_path, 'r') as f:
            data = yaml.safe_load(f)
        assert "llm" in data


if __name__ == "__main__":
    pytest.main([__file__])
