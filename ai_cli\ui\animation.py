"""Ball animation with elapsed timer for processing indication."""

import asyncio
import time
from typing import Optional

from rich.console import Console
from rich.live import Live
from rich.text import Text


class BallAnimation:
    """Animated ball with elapsed time counter for processing indication."""
    
    def __init__(self, console: Console) -> None:
        """Initialize ball animation.
        
        Args:
            console: Rich console instance
        """
        self.console = console
        self.is_running = False
        self.start_time: Optional[float] = None
        self.live: Optional[Live] = None
        self.task: Optional[asyncio.Task] = None
        
        # Animation frames
        self.frames = ["⠋", "⠙", "⠹", "⠸", "⠼", "⠴", "⠦", "⠧", "⠇", "⠏"]
        self.current_frame = 0
    
    async def start(self, message: str = "Processing") -> None:
        """Start the animation.
        
        Args:
            message: Message to display with animation
        """
        if self.is_running:
            return
        
        self.is_running = True
        self.start_time = time.time()
        self.current_frame = 0
        
        # Create live display
        self.live = Live(
            self._get_display_text(message, 0),
            console=self.console,
            refresh_per_second=10,
            transient=True,
        )
        
        self.live.start()
        
        # Start animation task
        self.task = asyncio.create_task(self._animate(message))
    
    async def stop(self) -> None:
        """Stop the animation."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        if self.live:
            self.live.stop()
            self.live = None
    
    async def _animate(self, message: str) -> None:
        """Animation loop.
        
        Args:
            message: Message to display
        """
        try:
            while self.is_running:
                elapsed = time.time() - (self.start_time or 0)
                
                if self.live:
                    self.live.update(self._get_display_text(message, elapsed))
                
                self.current_frame = (self.current_frame + 1) % len(self.frames)
                await asyncio.sleep(0.1)  # 100ms between frames
                
        except asyncio.CancelledError:
            pass
    
    def _get_display_text(self, message: str, elapsed: float) -> Text:
        """Get the display text with animation and timer.
        
        Args:
            message: Message to display
            elapsed: Elapsed time in seconds
            
        Returns:
            Rich Text object
        """
        frame = self.frames[self.current_frame]
        elapsed_str = f"{elapsed:.1f}s"
        
        text = Text()
        text.append(frame, style="cyan bold")
        text.append(f" {message} ", style="white")
        text.append(f"[{elapsed_str}]", style="dim")
        
        return text
    
    def __enter__(self) -> "BallAnimation":
        """Context manager entry."""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb) -> None:
        """Context manager exit."""
        if self.is_running:
            # For synchronous context manager, we can't await
            # The caller should use async context manager or call stop() manually
            pass


class AsyncBallAnimation:
    """Async context manager version of BallAnimation."""
    
    def __init__(self, animation: BallAnimation) -> None:
        """Initialize async wrapper.
        
        Args:
            animation: BallAnimation instance
        """
        self.animation = animation
        self.message = ""
    
    async def __aenter__(self) -> BallAnimation:
        """Async context manager entry."""
        await self.animation.start(self.message)
        return self.animation
    
    async def __aexit__(self, exc_type, exc_val, exc_tb) -> None:
        """Async context manager exit."""
        await self.animation.stop()


def create_ball_animation(console: Console, message: str = "Processing") -> AsyncBallAnimation:
    """Create an async ball animation context manager.
    
    Args:
        console: Rich console instance
        message: Message to display
        
    Returns:
        Async context manager for ball animation
    """
    animation = BallAnimation(console)
    async_animation = AsyncBallAnimation(animation)
    async_animation.message = message
    return async_animation
