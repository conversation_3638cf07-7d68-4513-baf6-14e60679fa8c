"""Prompt engineering and management for LLM interactions."""

import platform
from typing import Any, Dict, List

from ai_cli.config.models import Config
from ai_cli.llm.providers.base import LLMMessage


class PromptManager:
    """Manages prompt construction and engineering for LLM interactions."""
    
    def __init__(self, config: Config) -> None:
        """Initialize prompt manager.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.system_prompt = self._build_system_prompt()
    
    def _build_system_prompt(self) -> str:
        """Build the main system prompt that defines AI behavior.
        
        Returns:
            System prompt string
        """
        os_info = platform.system()
        
        prompt = f"""You are an expert AI CLI assistant with autonomous task execution capabilities. Your goal is to help users accomplish tasks on their {os_info} system through intelligent command execution, file operations, and information retrieval.

**Core Capabilities:**
- Execute shell commands safely and efficiently
- Perform file system operations (read, write, create, delete, move)
- Search the web for information and summarize results
- Analyze and manipulate code and text files
- Provide intelligent suggestions and explanations
- Autonomously decompose complex tasks into executable steps

**Autonomous Behavior:**
- For complex requests, break them down into a sequence of steps
- Use available tools to gather information, execute commands, and manipulate files
- Adapt your plan based on the results of previous steps
- If a step fails, analyze the error and try alternative approaches
- Always explain what you're doing and why

**Available Tools:**
You have access to various tools for system interaction. Use them by making function calls when appropriate. You can chain multiple tool calls to achieve complex goals.

**Safety Guidelines:**
- Always explain potentially destructive operations before executing them
- Be cautious with file deletions and system modifications
- Respect user permissions and system security
- If unsure about a command's safety, ask for confirmation

**Response Style:**
- Be concise but informative
- Use markdown formatting for better readability
- Provide clear explanations of actions taken
- Show command outputs when relevant
- Offer suggestions for next steps when appropriate

**Current Environment:**
- Operating System: {os_info}
- Shell Command Confirmation: {'Enabled' if self.config.security.require_command_confirmation else 'Disabled'}
- Context Awareness: {'Enabled' if self.config.context.auto_context else 'Disabled'}

Remember: You can autonomously execute sequences of operations to complete complex tasks. Always prioritize user safety and system integrity."""
        
        return prompt
    
    def build_messages(
        self,
        query: str,
        context: Dict[str, Any],
        history: List[Dict[str, Any]],
        max_history: int = 10,
    ) -> List[LLMMessage]:
        """Build message list for LLM interaction.
        
        Args:
            query: User query
            context: Current context information
            history: Conversation history
            max_history: Maximum number of history entries to include
            
        Returns:
            List of LLM messages
        """
        messages = []
        
        # Add system prompt
        system_content = self.system_prompt
        
        # Add context information to system prompt
        if context:
            context_info = self._format_context(context)
            if context_info:
                system_content += f"\n\n**Current Context:**\n{context_info}"
        
        messages.append(LLMMessage(role="system", content=system_content))
        
        # Add conversation history (limited)
        recent_history = history[-max_history:] if history else []
        for entry in recent_history:
            messages.append(LLMMessage(role="user", content=entry["user_input"]))
            messages.append(LLMMessage(role="assistant", content=entry["ai_response"]))
        
        # Add current user query
        messages.append(LLMMessage(role="user", content=query))
        
        return messages
    
    def _format_context(self, context: Dict[str, Any]) -> str:
        """Format context information for inclusion in prompts.
        
        Args:
            context: Context dictionary
            
        Returns:
            Formatted context string
        """
        context_parts = []
        
        # Current directory
        if "current_directory" in context:
            context_parts.append(f"Current Directory: `{context['current_directory']}`")
        
        # Git information
        if "git_info" in context and context["git_info"]:
            git_info = context["git_info"]
            git_parts = []
            
            if "branch" in git_info:
                git_parts.append(f"Branch: {git_info['branch']}")
            if "status" in git_info:
                git_parts.append(f"Status: {git_info['status']}")
            if "uncommitted_changes" in git_info:
                git_parts.append(f"Uncommitted files: {git_info['uncommitted_changes']}")
            
            if git_parts:
                context_parts.append(f"Git Info: {', '.join(git_parts)}")
        
        # Files in context
        if "files" in context and context["files"]:
            files_info = []
            for file_info in context["files"][:10]:  # Limit to first 10 files
                path = file_info.get("path", "")
                file_type = file_info.get("type", "")
                size = file_info.get("size", "")
                
                if size:
                    files_info.append(f"`{path}` ({file_type}, {size})")
                else:
                    files_info.append(f"`{path}` ({file_type})")
            
            if files_info:
                context_parts.append(f"Relevant Files:\n" + "\n".join(f"- {info}" for info in files_info))
        
        # Directory listing
        if "directory_listing" in context and context["directory_listing"]:
            listing = context["directory_listing"][:20]  # Limit to first 20 items
            dir_items = [f"- `{item['name']}` ({item['type']})" for item in listing]
            context_parts.append(f"Directory Contents:\n" + "\n".join(dir_items))
        
        # Environment variables (if relevant)
        if "environment" in context and context["environment"]:
            env_vars = []
            for key, value in context["environment"].items():
                if key in ["PATH", "HOME", "USER", "PWD"]:  # Only show relevant env vars
                    env_vars.append(f"{key}={value}")
            
            if env_vars:
                context_parts.append(f"Environment: {', '.join(env_vars)}")
        
        return "\n\n".join(context_parts) if context_parts else ""
    
    def create_tool_execution_prompt(
        self,
        tool_name: str,
        arguments: Dict[str, Any],
        context: str = "",
    ) -> str:
        """Create a prompt for tool execution explanation.
        
        Args:
            tool_name: Name of the tool being executed
            arguments: Tool arguments
            context: Additional context
            
        Returns:
            Formatted prompt for tool execution
        """
        prompt = f"Executing tool: **{tool_name}**\n"
        
        if arguments:
            args_str = ", ".join([f"{k}={v}" for k, v in arguments.items()])
            prompt += f"Arguments: {args_str}\n"
        
        if context:
            prompt += f"Context: {context}\n"
        
        return prompt
    
    def create_error_recovery_prompt(
        self,
        error: str,
        attempted_action: str,
        context: Dict[str, Any],
    ) -> str:
        """Create a prompt for error recovery and alternative approaches.
        
        Args:
            error: Error message
            attempted_action: What was attempted
            context: Current context
            
        Returns:
            Error recovery prompt
        """
        prompt = f"""An error occurred while attempting: {attempted_action}

Error: {error}

Please analyze this error and suggest alternative approaches or solutions. Consider:
1. What might have caused this error?
2. Are there alternative ways to achieve the same goal?
3. What additional information might be needed?
4. Should we try a different approach or ask the user for clarification?

Current context: {self._format_context(context)}"""
        
        return prompt
    
    def create_task_planning_prompt(
        self,
        task_description: str,
        context: Dict[str, Any],
        available_tools: List[str],
    ) -> str:
        """Create a prompt for autonomous task planning.
        
        Args:
            task_description: Description of the task to accomplish
            context: Current context
            available_tools: List of available tool names
            
        Returns:
            Task planning prompt
        """
        tools_list = ", ".join(available_tools)
        
        prompt = f"""Task: {task_description}

Please create a detailed plan to accomplish this task. Break it down into specific steps that can be executed using the available tools.

Available tools: {tools_list}

Current context: {self._format_context(context)}

For each step in your plan:
1. Clearly state what you want to accomplish
2. Identify which tool(s) to use
3. Specify the exact arguments needed
4. Consider potential error conditions and alternatives
5. Explain how this step contributes to the overall goal

Begin executing the plan step by step, adapting as needed based on the results of each step."""
        
        return prompt
