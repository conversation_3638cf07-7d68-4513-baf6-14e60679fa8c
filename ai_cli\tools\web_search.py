"""Web search tool for finding information online."""

import asyncio
import logging
from typing import Any, Dict, List
from urllib.parse import quote_plus

import httpx
from bs4 import BeautifulSoup
import trafilatura

from ai_cli.config.models import Config
from ai_cli.utils.exceptions import WebSearchError

logger = logging.getLogger(__name__)


class WebSearchTool:
    """Tool for web search and content extraction."""
    
    def __init__(self, config: Config) -> None:
        """Initialize web search tool.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.provider = config.web_search.provider
        self.api_key = config.web_search.api_key
        self.timeout = config.web_search.timeout
        
        self.client = httpx.AsyncClient(
            timeout=self.timeout,
            headers={
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )
        
        logger.info(f"Web search tool initialized with provider: {self.provider}")
    
    async def search_web(
        self,
        query: str,
        num_results: int = 5,
    ) -> Dict[str, Any]:
        """Search the web for information.
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            Dictionary containing search results
        """
        try:
            if self.provider == "duckduckgo":
                return await self._search_duckduckgo(query, num_results)
            elif self.provider == "google" and self.api_key:
                return await self._search_google(query, num_results)
            else:
                # Fallback to DuckDuckGo
                return await self._search_duckduckgo(query, num_results)
                
        except Exception as e:
            logger.error(f"Error searching web for '{query}': {e}")
            return {
                "success": False,
                "error": f"Web search failed: {str(e)}",
                "query": query,
                "results": [],
            }
    
    async def _search_duckduckgo(
        self,
        query: str,
        num_results: int,
    ) -> Dict[str, Any]:
        """Search using DuckDuckGo (no API key required).
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            Search results dictionary
        """
        try:
            # DuckDuckGo instant answer API
            params = {
                "q": query,
                "format": "json",
                "no_html": "1",
                "skip_disambig": "1",
            }
            
            response = await self.client.get(
                "https://api.duckduckgo.com/",
                params=params
            )
            response.raise_for_status()
            
            data = response.json()
            results = []
            
            # Extract instant answer if available
            if data.get("Abstract"):
                results.append({
                    "title": data.get("Heading", "DuckDuckGo Instant Answer"),
                    "url": data.get("AbstractURL", ""),
                    "snippet": data.get("Abstract", ""),
                    "source": "instant_answer",
                })
            
            # Extract related topics
            for topic in data.get("RelatedTopics", [])[:num_results]:
                if isinstance(topic, dict) and "Text" in topic:
                    results.append({
                        "title": topic.get("Text", "").split(" - ")[0],
                        "url": topic.get("FirstURL", ""),
                        "snippet": topic.get("Text", ""),
                        "source": "related_topic",
                    })
            
            # If we don't have enough results, try HTML scraping (less reliable)
            if len(results) < num_results:
                html_results = await self._scrape_duckduckgo_html(query, num_results - len(results))
                results.extend(html_results)
            
            logger.info(f"DuckDuckGo search for '{query}' returned {len(results)} results")
            
            return {
                "success": True,
                "query": query,
                "results": results[:num_results],
                "provider": "duckduckgo",
                "total_results": len(results),
            }
            
        except Exception as e:
            logger.error(f"DuckDuckGo search error: {e}")
            raise WebSearchError(f"DuckDuckGo search failed: {str(e)}")
    
    async def _scrape_duckduckgo_html(
        self,
        query: str,
        num_results: int,
    ) -> List[Dict[str, Any]]:
        """Scrape DuckDuckGo HTML results (fallback method).
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            List of search results
        """
        try:
            url = f"https://html.duckduckgo.com/html/?q={quote_plus(query)}"
            response = await self.client.get(url)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.text, 'html.parser')
            results = []
            
            # Find result containers
            result_containers = soup.find_all('div', class_='result')
            
            for container in result_containers[:num_results]:
                try:
                    # Extract title and URL
                    title_link = container.find('a', class_='result__a')
                    if not title_link:
                        continue
                    
                    title = title_link.get_text(strip=True)
                    url = title_link.get('href', '')
                    
                    # Extract snippet
                    snippet_elem = container.find('a', class_='result__snippet')
                    snippet = snippet_elem.get_text(strip=True) if snippet_elem else ""
                    
                    if title and url:
                        results.append({
                            "title": title,
                            "url": url,
                            "snippet": snippet,
                            "source": "html_scrape",
                        })
                        
                except Exception as e:
                    logger.debug(f"Error parsing result container: {e}")
                    continue
            
            return results
            
        except Exception as e:
            logger.error(f"Error scraping DuckDuckGo HTML: {e}")
            return []
    
    async def _search_google(
        self,
        query: str,
        num_results: int,
    ) -> Dict[str, Any]:
        """Search using Google Custom Search API.
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            Search results dictionary
        """
        try:
            # This would require Google Custom Search API setup
            # For now, return a placeholder
            return {
                "success": False,
                "error": "Google Custom Search API not implemented yet",
                "query": query,
                "results": [],
            }
            
        except Exception as e:
            logger.error(f"Google search error: {e}")
            raise WebSearchError(f"Google search failed: {str(e)}")
    
    async def fetch_webpage(
        self,
        url: str,
        timeout: int = 10,
    ) -> Dict[str, Any]:
        """Fetch and extract content from a webpage.
        
        Args:
            url: URL to fetch
            timeout: Request timeout in seconds
            
        Returns:
            Dictionary containing webpage content
        """
        try:
            response = await self.client.get(url, timeout=timeout)
            response.raise_for_status()
            
            # Extract main content using trafilatura
            content = trafilatura.extract(
                response.text,
                include_comments=False,
                include_tables=True,
                include_formatting=True,
            )
            
            if not content:
                # Fallback to BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # Remove script and style elements
                for script in soup(["script", "style"]):
                    script.decompose()
                
                # Get text content
                content = soup.get_text()
                
                # Clean up whitespace
                lines = (line.strip() for line in content.splitlines())
                chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
                content = ' '.join(chunk for chunk in chunks if chunk)
            
            # Get page title
            soup = BeautifulSoup(response.text, 'html.parser')
            title = soup.title.string.strip() if soup.title else url
            
            # Limit content length
            max_content_length = 10000  # 10KB
            if len(content) > max_content_length:
                content = content[:max_content_length] + "... [content truncated]"
            
            logger.info(f"Fetched webpage: {url} ({len(content)} chars)")
            
            return {
                "success": True,
                "url": url,
                "title": title,
                "content": content,
                "content_length": len(content),
                "status_code": response.status_code,
            }
            
        except httpx.TimeoutException:
            logger.error(f"Timeout fetching webpage: {url}")
            return {
                "success": False,
                "error": f"Request timed out after {timeout} seconds",
                "url": url,
                "content": "",
            }
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching {url}: {e.response.status_code}")
            return {
                "success": False,
                "error": f"HTTP {e.response.status_code}: {e.response.reason_phrase}",
                "url": url,
                "content": "",
                "status_code": e.response.status_code,
            }
        except Exception as e:
            logger.error(f"Error fetching webpage {url}: {e}")
            return {
                "success": False,
                "error": f"Failed to fetch webpage: {str(e)}",
                "url": url,
                "content": "",
            }
    
    async def cleanup(self) -> None:
        """Clean up HTTP client."""
        await self.client.aclose()
