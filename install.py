#!/usr/bin/env python3
"""Installation script for AI CLI Terminal Tool."""

import os
import subprocess
import sys
from pathlib import Path


def run_command(command, check=True):
    """Run a shell command."""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, check=check)
    return result.returncode == 0


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("Error: Python 3.8 or higher is required.")
        sys.exit(1)
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor} detected")


def check_poetry():
    """Check if Poetry is available."""
    try:
        subprocess.run(["poetry", "--version"], check=True, capture_output=True)
        print("✓ Poetry detected")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("Poetry not found. Will use pip instead.")
        return False


def install_with_poetry():
    """Install using Poetry."""
    print("\n📦 Installing with Poetry...")
    
    if not run_command("poetry install"):
        print("Error: Poetry installation failed.")
        return False
    
    print("✓ Dependencies installed with Poetry")
    return True


def install_with_pip():
    """Install using pip."""
    print("\n📦 Installing with pip...")
    
    # Install in development mode
    if not run_command("pip install -e ."):
        print("Error: pip installation failed.")
        return False
    
    print("✓ Dependencies installed with pip")
    return True


def create_config():
    """Create initial configuration."""
    print("\n⚙️ Setting up configuration...")
    
    config_dir = Path.home() / ".config" / "ai_cli_tool"
    config_file = config_dir / "config.yaml"
    
    if config_file.exists():
        print(f"✓ Configuration already exists at {config_file}")
        return True
    
    try:
        config_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy example config
        example_config = Path("config") / "config.example.yaml"
        if example_config.exists():
            import shutil
            shutil.copy(example_config, config_file)
            print(f"✓ Configuration created at {config_file}")
            print("  Please edit this file to add your API keys and preferences.")
        else:
            print("Warning: Example configuration not found.")
            
    except Exception as e:
        print(f"Error creating configuration: {e}")
        return False
    
    return True


def check_ollama():
    """Check if Ollama is available."""
    try:
        result = subprocess.run(
            ["curl", "-s", "http://localhost:11434/api/tags"],
            capture_output=True,
            timeout=5
        )
        if result.returncode == 0:
            print("✓ Ollama server detected at localhost:11434")
            return True
    except (subprocess.CalledProcessError, FileNotFoundError, subprocess.TimeoutExpired):
        pass
    
    print("ℹ️ Ollama not detected. You can:")
    print("   - Install Ollama from https://ollama.ai")
    print("   - Or use Deepseek API (add API key to config)")
    return False


def main():
    """Main installation function."""
    print("🤖 AI CLI Terminal Tool - Installation")
    print("=" * 40)
    
    # Check Python version
    check_python_version()
    
    # Install dependencies
    has_poetry = check_poetry()
    
    if has_poetry:
        success = install_with_poetry()
    else:
        success = install_with_pip()
    
    if not success:
        print("\n❌ Installation failed!")
        sys.exit(1)
    
    # Create configuration
    create_config()
    
    # Check for LLM providers
    check_ollama()
    
    print("\n🎉 Installation completed!")
    print("\nNext steps:")
    print("1. Edit ~/.config/ai_cli_tool/config.yaml to configure your LLM provider")
    print("2. Run 'ai-cli' to start the tool")
    print("3. Type '/help' for available commands")
    
    if has_poetry:
        print("\nDevelopment commands:")
        print("- poetry run ai-cli    # Run the tool")
        print("- poetry run pytest    # Run tests")
        print("- poetry run black .   # Format code")


if __name__ == "__main__":
    main()
