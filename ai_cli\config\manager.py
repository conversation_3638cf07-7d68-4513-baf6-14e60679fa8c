"""Configuration manager for loading and saving configuration."""

import os
from pathlib import Path
from typing import Any, Dict, Optional

import yaml
from dotenv import load_dotenv

from ai_cli.config.models import Config, LLMConfig, UIConfig, ContextConfig, SecurityConfig, SessionConfig, WebSearchConfig


class ConfigManager:
    """Manages configuration loading, saving, and validation."""
    
    DEFAULT_CONFIG_PATHS = [
        Path.home() / ".config" / "ai_cli_tool" / "config.yaml",
        Path.home() / ".ai_cli_tool" / "config.yaml",
        Path.cwd() / "config.yaml",
    ]
    
    def __init__(self, config_path: Optional[Path] = None) -> None:
        """Initialize configuration manager.
        
        Args:
            config_path: Optional path to configuration file
        """
        self.config_path = config_path
        load_dotenv()  # Load environment variables from .env file
    
    def load_config(self) -> Config:
        """Load configuration from file or create default.
        
        Returns:
            Loaded configuration object
        """
        config_path = self._find_config_path()
        
        if config_path and config_path.exists():
            return self._load_from_file(config_path)
        else:
            # Create default configuration
            config = self._create_default_config()
            # Try to save default config
            try:
                default_path = self.DEFAULT_CONFIG_PATHS[0]
                default_path.parent.mkdir(parents=True, exist_ok=True)
                self._save_to_file(config, default_path)
            except Exception:
                pass  # Ignore save errors for default config
            return config
    
    def save_config(self, config: Config, path: Optional[Path] = None) -> None:
        """Save configuration to file.
        
        Args:
            config: Configuration object to save
            path: Optional path to save to
        """
        save_path = path or self._find_config_path() or self.DEFAULT_CONFIG_PATHS[0]
        save_path.parent.mkdir(parents=True, exist_ok=True)
        self._save_to_file(config, save_path)
    
    def init_config(self, path: Optional[Path] = None, force: bool = False) -> Path:
        """Initialize configuration file with default settings.
        
        Args:
            path: Optional path to create configuration file
            force: Whether to overwrite existing configuration
            
        Returns:
            Path to created configuration file
        """
        config_path = path or self.DEFAULT_CONFIG_PATHS[0]
        
        if config_path.exists() and not force:
            raise FileExistsError(f"Configuration file already exists: {config_path}")
        
        config = self._create_default_config()
        config_path.parent.mkdir(parents=True, exist_ok=True)
        self._save_to_file(config, config_path)
        
        return config_path
    
    def _find_config_path(self) -> Optional[Path]:
        """Find configuration file path.
        
        Returns:
            Path to configuration file or None if not found
        """
        if self.config_path:
            return self.config_path
        
        for path in self.DEFAULT_CONFIG_PATHS:
            if path.exists():
                return path
        
        return None
    
    def _load_from_file(self, path: Path) -> Config:
        """Load configuration from YAML file.
        
        Args:
            path: Path to configuration file
            
        Returns:
            Loaded configuration object
        """
        with open(path, 'r', encoding='utf-8') as f:
            data = yaml.safe_load(f) or {}
        
        return self._dict_to_config(data, path.parent)
    
    def _save_to_file(self, config: Config, path: Path) -> None:
        """Save configuration to YAML file.
        
        Args:
            config: Configuration object to save
            path: Path to save configuration file
        """
        data = self._config_to_dict(config)
        
        with open(path, 'w', encoding='utf-8') as f:
            yaml.dump(data, f, default_flow_style=False, indent=2)
    
    def _create_default_config(self) -> Config:
        """Create default configuration with environment variable overrides.
        
        Returns:
            Default configuration object
        """
        config = Config()
        
        # Override with environment variables
        if deepseek_key := os.getenv("DEEPSEEK_API_KEY"):
            config.llm.deepseek["api_key"] = deepseek_key
        
        if ollama_url := os.getenv("OLLAMA_BASE_URL"):
            config.llm.ollama["base_url"] = ollama_url
        
        if web_search_key := os.getenv("WEB_SEARCH_API_KEY"):
            config.web_search.api_key = web_search_key
        
        return config
    
    def _dict_to_config(self, data: Dict[str, Any], config_dir: Path) -> Config:
        """Convert dictionary to configuration object.
        
        Args:
            data: Configuration dictionary
            config_dir: Configuration directory path
            
        Returns:
            Configuration object
        """
        config = Config()
        config.config_dir = config_dir
        
        if "llm" in data:
            llm_data = data["llm"]
            config.llm = LLMConfig(
                default_provider=llm_data.get("default_provider", "ollama"),
                deepseek=llm_data.get("deepseek", config.llm.deepseek),
                ollama=llm_data.get("ollama", config.llm.ollama),
            )
        
        if "ui" in data:
            ui_data = data["ui"]
            config.ui = UIConfig(**{k: v for k, v in ui_data.items() if hasattr(UIConfig, k)})
        
        if "context" in data:
            context_data = data["context"]
            config.context = ContextConfig(**{k: v for k, v in context_data.items() if hasattr(ContextConfig, k)})
        
        if "security" in data:
            security_data = data["security"]
            config.security = SecurityConfig(**{k: v for k, v in security_data.items() if hasattr(SecurityConfig, k)})
        
        if "session" in data:
            session_data = data["session"]
            config.session = SessionConfig(**{k: v for k, v in session_data.items() if hasattr(SessionConfig, k)})
        
        if "web_search" in data:
            web_search_data = data["web_search"]
            config.web_search = WebSearchConfig(**{k: v for k, v in web_search_data.items() if hasattr(WebSearchConfig, k)})
        
        return config
    
    def _config_to_dict(self, config: Config) -> Dict[str, Any]:
        """Convert configuration object to dictionary.
        
        Args:
            config: Configuration object
            
        Returns:
            Configuration dictionary
        """
        return {
            "llm": {
                "default_provider": config.llm.default_provider,
                "deepseek": config.llm.deepseek,
                "ollama": config.llm.ollama,
            },
            "ui": {
                "enable_animation": config.ui.enable_animation,
                "confirm_shell_commands": config.ui.confirm_shell_commands,
                "animation_speed": config.ui.animation_speed,
                "max_output_lines": config.ui.max_output_lines,
                "syntax_highlighting": config.ui.syntax_highlighting,
                "show_timestamps": config.ui.show_timestamps,
            },
            "context": {
                "auto_context": config.context.auto_context,
                "max_file_size": config.context.max_file_size,
                "exclude_patterns": config.context.exclude_patterns,
                "include_git_info": config.context.include_git_info,
                "max_context_files": config.context.max_context_files,
            },
            "security": {
                "require_command_confirmation": config.security.require_command_confirmation,
                "allowed_commands": config.security.allowed_commands,
                "blocked_commands": config.security.blocked_commands,
                "max_command_length": config.security.max_command_length,
            },
            "session": {
                "auto_save": config.session.auto_save,
                "max_history_entries": config.session.max_history_entries,
                "session_timeout": config.session.session_timeout,
                "persist_context": config.session.persist_context,
            },
            "web_search": {
                "enabled": config.web_search.enabled,
                "provider": config.web_search.provider,
                "api_key": config.web_search.api_key,
                "max_results": config.web_search.max_results,
                "timeout": config.web_search.timeout,
            },
        }
