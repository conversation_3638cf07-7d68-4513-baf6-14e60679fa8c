"""Diff viewer for displaying text differences in the CLI."""

import difflib
from typing import List, Optional, Tuple

from rich.console import Console
from rich.syntax import Syntax
from rich.text import Text


class DiffViewer:
    """Displays text differences with syntax highlighting."""
    
    def __init__(self, console: Console) -> None:
        """Initialize diff viewer.
        
        Args:
            console: Rich console instance
        """
        self.console = console
    
    def show_diff(
        self,
        old_text: str,
        new_text: str,
        old_label: str = "Original",
        new_label: str = "Modified",
        context_lines: int = 3,
        syntax: Optional[str] = None,
    ) -> None:
        """Display a unified diff between two texts.
        
        Args:
            old_text: Original text
            new_text: Modified text
            old_label: Label for original text
            new_label: Label for modified text
            context_lines: Number of context lines to show
            syntax: Optional syntax highlighting language
        """
        # Split texts into lines
        old_lines = old_text.splitlines(keepends=True)
        new_lines = new_text.splitlines(keepends=True)
        
        # Generate unified diff
        diff_lines = list(difflib.unified_diff(
            old_lines,
            new_lines,
            fromfile=old_label,
            tofile=new_label,
            n=context_lines,
        ))
        
        if not diff_lines:
            self.console.print("[dim]No differences found[/dim]")
            return
        
        # Display diff with syntax highlighting
        self._display_diff_lines(diff_lines, syntax)
    
    def show_side_by_side_diff(
        self,
        old_text: str,
        new_text: str,
        old_label: str = "Original",
        new_label: str = "Modified",
        width: Optional[int] = None,
    ) -> None:
        """Display a side-by-side diff between two texts.
        
        Args:
            old_text: Original text
            new_text: Modified text
            old_label: Label for original text
            new_label: Label for modified text
            width: Optional width for each column
        """
        # Split texts into lines
        old_lines = old_text.splitlines()
        new_lines = new_text.splitlines()
        
        # Calculate column width
        if width is None:
            console_width = self.console.size.width
            width = (console_width - 5) // 2  # Account for separator
        
        # Display headers
        self.console.print(f"[bold]{old_label:<{width}} | {new_label}[/bold]")
        self.console.print("─" * width + "─┼─" + "─" * width)
        
        # Display lines side by side
        max_lines = max(len(old_lines), len(new_lines))
        
        for i in range(max_lines):
            old_line = old_lines[i] if i < len(old_lines) else ""
            new_line = new_lines[i] if i < len(new_lines) else ""
            
            # Truncate lines if too long
            if len(old_line) > width:
                old_line = old_line[:width-3] + "..."
            if len(new_line) > width:
                new_line = new_line[:width-3] + "..."
            
            # Determine line colors
            old_style = ""
            new_style = ""
            
            if old_line != new_line:
                if not old_line:
                    new_style = "green"
                elif not new_line:
                    old_style = "red"
                else:
                    old_style = "red"
                    new_style = "green"
            
            # Format and display
            old_formatted = f"[{old_style}]{old_line:<{width}}[/{old_style}]" if old_style else f"{old_line:<{width}}"
            new_formatted = f"[{new_style}]{new_line}[/{new_style}]" if new_style else new_line
            
            self.console.print(f"{old_formatted} │ {new_formatted}")
    
    def _display_diff_lines(self, diff_lines: List[str], syntax: Optional[str] = None) -> None:
        """Display unified diff lines with appropriate coloring.
        
        Args:
            diff_lines: Lines from unified diff
            syntax: Optional syntax highlighting language
        """
        for line in diff_lines:
            line = line.rstrip('\n')
            
            if line.startswith('+++') or line.startswith('---'):
                # File headers
                self.console.print(line, style="bold blue")
            elif line.startswith('@@'):
                # Hunk headers
                self.console.print(line, style="bold cyan")
            elif line.startswith('+'):
                # Added lines
                content = line[1:]  # Remove the + prefix
                if syntax:
                    try:
                        syntax_obj = Syntax(content, syntax, theme="monokai", line_numbers=False)
                        text = Text()
                        text.append("+ ", style="green bold")
                        # Note: This is a simplified approach. For full syntax highlighting
                        # of diff content, we'd need more complex handling
                        text.append(content, style="green")
                        self.console.print(text)
                    except Exception:
                        self.console.print(line, style="green")
                else:
                    self.console.print(line, style="green")
            elif line.startswith('-'):
                # Removed lines
                content = line[1:]  # Remove the - prefix
                if syntax:
                    try:
                        text = Text()
                        text.append("- ", style="red bold")
                        text.append(content, style="red")
                        self.console.print(text)
                    except Exception:
                        self.console.print(line, style="red")
                else:
                    self.console.print(line, style="red")
            else:
                # Context lines
                self.console.print(line, style="dim")
    
    def compare_files(
        self,
        file1_path: str,
        file2_path: str,
        context_lines: int = 3,
        syntax: Optional[str] = None,
    ) -> None:
        """Compare two files and display the diff.
        
        Args:
            file1_path: Path to first file
            file2_path: Path to second file
            context_lines: Number of context lines to show
            syntax: Optional syntax highlighting language
        """
        try:
            with open(file1_path, 'r', encoding='utf-8') as f1:
                text1 = f1.read()
            
            with open(file2_path, 'r', encoding='utf-8') as f2:
                text2 = f2.read()
            
            self.show_diff(
                text1,
                text2,
                old_label=file1_path,
                new_label=file2_path,
                context_lines=context_lines,
                syntax=syntax,
            )
            
        except FileNotFoundError as e:
            self.console.print(f"[red]Error: File not found - {e}[/red]")
        except Exception as e:
            self.console.print(f"[red]Error comparing files: {e}[/red]")
    
    def show_inline_diff(
        self,
        old_text: str,
        new_text: str,
        old_label: str = "Original",
        new_label: str = "Modified",
    ) -> None:
        """Show character-level differences inline.
        
        Args:
            old_text: Original text
            new_text: Modified text
            old_label: Label for original text
            new_label: Label for modified text
        """
        # Use SequenceMatcher for character-level diff
        matcher = difflib.SequenceMatcher(None, old_text, new_text)
        
        self.console.print(f"[bold]{old_label}:[/bold]")
        old_display = Text()
        
        self.console.print(f"[bold]{new_label}:[/bold]")
        new_display = Text()
        
        for tag, i1, i2, j1, j2 in matcher.get_opcodes():
            old_chunk = old_text[i1:i2]
            new_chunk = new_text[j1:j2]
            
            if tag == 'equal':
                old_display.append(old_chunk)
                new_display.append(new_chunk)
            elif tag == 'delete':
                old_display.append(old_chunk, style="red bold")
            elif tag == 'insert':
                new_display.append(new_chunk, style="green bold")
            elif tag == 'replace':
                old_display.append(old_chunk, style="red bold")
                new_display.append(new_chunk, style="green bold")
        
        self.console.print(old_display)
        self.console.print(new_display)
