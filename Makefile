# AI CLI Terminal Tool - Makefile

.PHONY: help install install-dev test format lint clean run config

# Default target
help:
	@echo "AI CLI Terminal Tool - Available Commands:"
	@echo ""
	@echo "  install     - Install the application"
	@echo "  install-dev - Install in development mode"
	@echo "  test        - Run tests"
	@echo "  format      - Format code with black"
	@echo "  lint        - Run linting with ruff"
	@echo "  clean       - Clean build artifacts"
	@echo "  run         - Run the application"
	@echo "  config      - Initialize configuration"
	@echo ""

# Installation
install:
	python install.py

install-dev:
	poetry install --with dev

# Testing
test:
	poetry run pytest tests/ -v

test-coverage:
	poetry run pytest tests/ --cov=ai_cli --cov-report=html

# Code quality
format:
	poetry run black ai_cli/ tests/
	poetry run ruff check --fix ai_cli/ tests/

lint:
	poetry run ruff check ai_cli/ tests/
	poetry run mypy ai_cli/

# Cleanup
clean:
	find . -type d -name "__pycache__" -exec rm -rf {} +
	find . -type f -name "*.pyc" -delete
	find . -type f -name "*.pyo" -delete
	rm -rf build/
	rm -rf dist/
	rm -rf *.egg-info/
	rm -rf .coverage
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/

# Running
run:
	poetry run ai-cli

run-debug:
	poetry run ai-cli --debug

# Configuration
config:
	poetry run ai-cli init-config

# Development
dev-setup: install-dev config
	@echo "Development environment ready!"
	@echo "Run 'make run' to start the application"

# Build
build:
	poetry build

# Docker (if you want to add Docker support later)
docker-build:
	docker build -t ai-cli-terminal .

docker-run:
	docker run -it --rm ai-cli-terminal
