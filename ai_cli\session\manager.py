"""Session manager for handling conversation history and state."""

import json
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from ai_cli.config.models import Config
from ai_cli.session.models import Session

logger = logging.getLogger(__name__)


class SessionManager:
    """Manages user sessions and conversation history."""
    
    def __init__(self, config: Config) -> None:
        """Initialize session manager.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.sessions: Dict[str, Session] = {}
        self.current_session_id: Optional[str] = None
        
        # Ensure session directory exists
        if config.session_dir:
            config.session_dir.mkdir(parents=True, exist_ok=True)
            self.session_dir = config.session_dir
        else:
            self.session_dir = Path.home() / ".config" / "ai_cli_tool" / "sessions"
            self.session_dir.mkdir(parents=True, exist_ok=True)
        
        # Load existing sessions
        self._load_sessions()
        
        logger.info(f"Session manager initialized with {len(self.sessions)} sessions")
    
    def create_session(self, name: Optional[str] = None) -> Session:
        """Create a new session.
        
        Args:
            name: Optional session name
            
        Returns:
            Created session
        """
        session = Session(name=name or "")
        self.sessions[session.id] = session
        self.current_session_id = session.id
        
        if self.config.session.auto_save:
            self._save_session(session)
        
        logger.info(f"Created new session: {session.name} ({session.id})")
        return session
    
    def get_session(self, session_id: str) -> Optional[Session]:
        """Get a session by ID.
        
        Args:
            session_id: Session identifier
            
        Returns:
            Session object or None if not found
        """
        return self.sessions.get(session_id)
    
    def get_current_session(self) -> Optional[Session]:
        """Get the current active session.
        
        Returns:
            Current session or None
        """
        if self.current_session_id:
            return self.sessions.get(self.current_session_id)
        return None
    
    def get_or_create_session(self, session_id: Optional[str] = None) -> Session:
        """Get existing session or create a new one.
        
        Args:
            session_id: Optional session ID to retrieve
            
        Returns:
            Session object
        """
        if session_id and session_id in self.sessions:
            self.current_session_id = session_id
            return self.sessions[session_id]
        
        if self.current_session_id and self.current_session_id in self.sessions:
            return self.sessions[self.current_session_id]
        
        # Create new session
        return self.create_session()
    
    def set_current_session(self, session_id: str) -> bool:
        """Set the current active session.
        
        Args:
            session_id: Session ID to set as current
            
        Returns:
            True if successful, False if session not found
        """
        if session_id in self.sessions:
            self.current_session_id = session_id
            logger.info(f"Set current session: {session_id}")
            return True
        return False
    
    def list_sessions(self) -> List[Session]:
        """List all sessions.
        
        Returns:
            List of all sessions
        """
        return list(self.sessions.values())
    
    def end_session(self, session_id: str) -> bool:
        """End a session.
        
        Args:
            session_id: Session ID to end
            
        Returns:
            True if successful, False if session not found
        """
        if session_id in self.sessions:
            session = self.sessions[session_id]
            session.is_active = False
            
            if self.config.session.auto_save:
                self._save_session(session)
            
            if self.current_session_id == session_id:
                self.current_session_id = None
            
            logger.info(f"Ended session: {session_id}")
            return True
        return False
    
    def add_interaction(
        self,
        session_id: str,
        user_input: str,
        ai_response: str,
        context: Optional[Dict[str, Any]] = None,
        execution_log: Optional[List[Dict[str, Any]]] = None,
    ) -> None:
        """Add an interaction to a session.
        
        Args:
            session_id: Session ID
            user_input: User's input
            ai_response: AI's response
            context: Optional context information
            execution_log: Optional execution log
        """
        session = self.sessions.get(session_id)
        if not session:
            logger.error(f"Session not found: {session_id}")
            return
        
        session.add_conversation_entry(
            user_input=user_input,
            ai_response=ai_response,
            context=context,
            execution_log=execution_log,
        )
        
        # Limit history size
        max_entries = self.config.session.max_history_entries
        if len(session.conversation_history) > max_entries:
            session.conversation_history = session.conversation_history[-max_entries:]
        
        if self.config.session.auto_save:
            self._save_session(session)
        
        logger.debug(f"Added interaction to session {session_id}")
    
    def get_conversation_history(
        self,
        session_id: str,
        limit: Optional[int] = None,
    ) -> List[Dict[str, Any]]:
        """Get conversation history for a session.
        
        Args:
            session_id: Session ID
            limit: Optional limit on number of entries
            
        Returns:
            List of conversation entries
        """
        session = self.sessions.get(session_id)
        if not session:
            return []
        
        history_limit = limit or 50  # Default to last 50 entries
        return session.get_recent_history(history_limit)
    
    def cleanup_old_sessions(self, days: int = 30) -> int:
        """Clean up old inactive sessions.
        
        Args:
            days: Number of days after which to clean up sessions
            
        Returns:
            Number of sessions cleaned up
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        sessions_to_remove = []
        
        for session_id, session in self.sessions.items():
            if (not session.is_active and 
                session.last_activity < cutoff_date):
                sessions_to_remove.append(session_id)
        
        for session_id in sessions_to_remove:
            self._delete_session_file(session_id)
            del self.sessions[session_id]
        
        logger.info(f"Cleaned up {len(sessions_to_remove)} old sessions")
        return len(sessions_to_remove)
    
    async def save_all_sessions(self) -> None:
        """Save all sessions to disk."""
        for session in self.sessions.values():
            self._save_session(session)
        
        logger.info(f"Saved {len(self.sessions)} sessions")
    
    def _load_sessions(self) -> None:
        """Load sessions from disk."""
        try:
            for session_file in self.session_dir.glob("*.json"):
                try:
                    with open(session_file, 'r', encoding='utf-8') as f:
                        session_data = json.load(f)
                    
                    session = Session.from_dict(session_data)
                    self.sessions[session.id] = session
                    
                    # Set most recent active session as current
                    if (session.is_active and 
                        (not self.current_session_id or 
                         session.last_activity > self.sessions[self.current_session_id].last_activity)):
                        self.current_session_id = session.id
                    
                except Exception as e:
                    logger.error(f"Error loading session from {session_file}: {e}")
                    continue
            
            logger.info(f"Loaded {len(self.sessions)} sessions from disk")
            
        except Exception as e:
            logger.error(f"Error loading sessions: {e}")
    
    def _save_session(self, session: Session) -> None:
        """Save a session to disk.
        
        Args:
            session: Session to save
        """
        try:
            session_file = self.session_dir / f"{session.id}.json"
            session_data = session.to_dict()
            
            with open(session_file, 'w', encoding='utf-8') as f:
                json.dump(session_data, f, indent=2, ensure_ascii=False)
            
            logger.debug(f"Saved session: {session.id}")
            
        except Exception as e:
            logger.error(f"Error saving session {session.id}: {e}")
    
    def _delete_session_file(self, session_id: str) -> None:
        """Delete a session file from disk.
        
        Args:
            session_id: Session ID to delete
        """
        try:
            session_file = self.session_dir / f"{session_id}.json"
            if session_file.exists():
                session_file.unlink()
                logger.debug(f"Deleted session file: {session_id}")
        except Exception as e:
            logger.error(f"Error deleting session file {session_id}: {e}")
