"""Session data models."""

import uuid
from dataclasses import dataclass, field
from datetime import datetime
from typing import Any, Dict, List, Optional


@dataclass
class ConversationEntry:
    """Represents a single conversation exchange."""
    timestamp: datetime
    user_input: str
    ai_response: str
    context: Optional[Dict[str, Any]] = None
    execution_log: Optional[List[Dict[str, Any]]] = None


@dataclass
class Session:
    """Represents a user session with conversation history."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = field(default="")
    created_at: datetime = field(default_factory=datetime.now)
    last_activity: datetime = field(default_factory=datetime.now)
    conversation_history: List[ConversationEntry] = field(default_factory=list)
    context_state: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True
    
    def __post_init__(self) -> None:
        """Set default name if not provided."""
        if not self.name:
            self.name = f"Session {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    def add_conversation_entry(
        self,
        user_input: str,
        ai_response: str,
        context: Optional[Dict[str, Any]] = None,
        execution_log: Optional[List[Dict[str, Any]]] = None,
    ) -> None:
        """Add a conversation entry to the session.
        
        Args:
            user_input: User's input
            ai_response: AI's response
            context: Optional context information
            execution_log: Optional execution log for autonomous tasks
        """
        entry = ConversationEntry(
            timestamp=datetime.now(),
            user_input=user_input,
            ai_response=ai_response,
            context=context,
            execution_log=execution_log,
        )
        
        self.conversation_history.append(entry)
        self.last_activity = datetime.now()
    
    def get_recent_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent conversation history.
        
        Args:
            limit: Maximum number of entries to return
            
        Returns:
            List of conversation entries as dictionaries
        """
        recent_entries = self.conversation_history[-limit:] if self.conversation_history else []
        
        return [
            {
                "timestamp": entry.timestamp.isoformat(),
                "user_input": entry.user_input,
                "ai_response": entry.ai_response,
                "context": entry.context,
                "execution_log": entry.execution_log,
            }
            for entry in recent_entries
        ]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert session to dictionary for serialization.
        
        Returns:
            Session as dictionary
        """
        return {
            "id": self.id,
            "name": self.name,
            "created_at": self.created_at.isoformat(),
            "last_activity": self.last_activity.isoformat(),
            "is_active": self.is_active,
            "context_state": self.context_state,
            "conversation_history": [
                {
                    "timestamp": entry.timestamp.isoformat(),
                    "user_input": entry.user_input,
                    "ai_response": entry.ai_response,
                    "context": entry.context,
                    "execution_log": entry.execution_log,
                }
                for entry in self.conversation_history
            ],
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Session":
        """Create session from dictionary.
        
        Args:
            data: Session data dictionary
            
        Returns:
            Session object
        """
        session = cls(
            id=data["id"],
            name=data["name"],
            created_at=datetime.fromisoformat(data["created_at"]),
            last_activity=datetime.fromisoformat(data["last_activity"]),
            is_active=data.get("is_active", True),
            context_state=data.get("context_state", {}),
        )
        
        # Restore conversation history
        for entry_data in data.get("conversation_history", []):
            entry = ConversationEntry(
                timestamp=datetime.fromisoformat(entry_data["timestamp"]),
                user_input=entry_data["user_input"],
                ai_response=entry_data["ai_response"],
                context=entry_data.get("context"),
                execution_log=entry_data.get("execution_log"),
            )
            session.conversation_history.append(entry)
        
        return session
