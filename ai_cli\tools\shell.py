"""Shell command execution tool."""

import asyncio
import logging
import platform
import subprocess
from pathlib import Path
from typing import Any, Dict, Optional

from ai_cli.config.models import Config
from ai_cli.utils.exceptions import CommandExecutionError

logger = logging.getLogger(__name__)


class ShellTool:
    """Tool for executing shell commands safely."""
    
    def __init__(self, config: Config) -> None:
        """Initialize shell tool.
        
        Args:
            config: Application configuration
        """
        self.config = config
        self.system = platform.system().lower()
        
        # Determine shell and command prefix
        if self.system == "windows":
            self.shell = "powershell"
            self.shell_args = ["-Command"]
        else:
            self.shell = "/bin/bash"
            self.shell_args = ["-c"]
        
        logger.info(f"Shell tool initialized for {self.system} using {self.shell}")
    
    async def execute_command(
        self,
        command: str,
        working_directory: Optional[str] = None,
        timeout: int = 30,
        session_id: str = "",
    ) -> Dict[str, Any]:
        """Execute a shell command.
        
        Args:
            command: Command to execute
            working_directory: Optional working directory
            timeout: Command timeout in seconds
            session_id: Session identifier for logging
            
        Returns:
            Dictionary containing execution results
        """
        # Security checks
        if not self._is_command_safe(command):
            return {
                "success": False,
                "error": f"Command blocked for security reasons: {command}",
                "stdout": "",
                "stderr": "",
                "exit_code": -1,
            }
        
        # Validate working directory
        if working_directory:
            work_dir = Path(working_directory)
            if not work_dir.exists() or not work_dir.is_dir():
                return {
                    "success": False,
                    "error": f"Working directory does not exist: {working_directory}",
                    "stdout": "",
                    "stderr": "",
                    "exit_code": -1,
                }
        else:
            work_dir = Path.cwd()
        
        try:
            logger.info(f"Executing command: {command} (session: {session_id})")
            
            # Prepare command for execution
            if self.system == "windows":
                # For Windows, we might want to use WSL for certain commands
                if self._should_use_wsl(command):
                    full_command = ["wsl", "-e", "bash", "-c", command]
                else:
                    full_command = [self.shell] + self.shell_args + [command]
            else:
                full_command = [self.shell] + self.shell_args + [command]
            
            # Execute command
            process = await asyncio.create_subprocess_exec(
                *full_command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=str(work_dir),
            )
            
            try:
                stdout, stderr = await asyncio.wait_for(
                    process.communicate(), timeout=timeout
                )
            except asyncio.TimeoutError:
                process.kill()
                await process.wait()
                return {
                    "success": False,
                    "error": f"Command timed out after {timeout} seconds",
                    "stdout": "",
                    "stderr": "",
                    "exit_code": -1,
                }
            
            # Decode output
            stdout_text = stdout.decode('utf-8', errors='replace').strip()
            stderr_text = stderr.decode('utf-8', errors='replace').strip()
            exit_code = process.returncode
            
            success = exit_code == 0
            
            result = {
                "success": success,
                "stdout": stdout_text,
                "stderr": stderr_text,
                "exit_code": exit_code,
                "command": command,
                "working_directory": str(work_dir),
            }
            
            if not success:
                result["error"] = f"Command failed with exit code {exit_code}"
                if stderr_text:
                    result["error"] += f": {stderr_text}"
            
            logger.info(f"Command completed: exit_code={exit_code}, success={success}")
            return result
            
        except Exception as e:
            logger.error(f"Error executing command '{command}': {e}")
            return {
                "success": False,
                "error": f"Failed to execute command: {str(e)}",
                "stdout": "",
                "stderr": "",
                "exit_code": -1,
                "command": command,
            }
    
    def _is_command_safe(self, command: str) -> bool:
        """Check if a command is safe to execute.
        
        Args:
            command: Command to check
            
        Returns:
            True if command is considered safe
        """
        command_lower = command.lower().strip()
        
        # Check blocked commands
        for blocked in self.config.security.blocked_commands:
            if blocked.lower() in command_lower:
                logger.warning(f"Blocked command detected: {command}")
                return False
        
        # Check command length
        if len(command) > self.config.security.max_command_length:
            logger.warning(f"Command too long: {len(command)} chars")
            return False
        
        # Additional safety checks
        dangerous_patterns = [
            "rm -rf /",
            "del /s /q",
            "format ",
            "fdisk",
            "mkfs",
            "dd if=",
            ":(){ :|:& };:",  # Fork bomb
            "sudo rm",
            "sudo dd",
            "sudo mkfs",
        ]
        
        for pattern in dangerous_patterns:
            if pattern in command_lower:
                logger.warning(f"Dangerous pattern detected: {pattern}")
                return False
        
        return True
    
    def _should_use_wsl(self, command: str) -> bool:
        """Determine if command should be executed in WSL on Windows.
        
        Args:
            command: Command to check
            
        Returns:
            True if command should use WSL
        """
        if self.system != "windows":
            return False
        
        # Commands that work better in WSL
        unix_commands = [
            "ls", "grep", "awk", "sed", "find", "xargs", "sort", "uniq",
            "head", "tail", "cat", "less", "more", "wc", "cut", "tr",
            "git", "curl", "wget", "ssh", "scp", "rsync", "tar", "gzip",
            "python", "pip", "node", "npm", "yarn", "docker", "kubectl",
        ]
        
        command_parts = command.split()
        if command_parts:
            first_command = command_parts[0]
            return first_command in unix_commands
        
        return False
    
    def get_shell_info(self) -> Dict[str, Any]:
        """Get information about the current shell environment.
        
        Returns:
            Shell environment information
        """
        return {
            "system": self.system,
            "shell": self.shell,
            "shell_args": self.shell_args,
            "supports_wsl": self.system == "windows",
            "working_directory": str(Path.cwd()),
        }
