"""Ollama LLM provider implementation."""

import json
import logging
from typing import Any, AsyncGenerator, Dict, List, Optional

import httpx

from ai_cli.llm.providers.base import BaseLL<PERSON>rovider, LLMMessage, LLMResponse
from ai_cli.utils.exceptions import LLMError

logger = logging.getLogger(__name__)


class OllamaProvider(BaseLLMProvider):
    """Ollama local LLM provider implementation."""
    
    def __init__(self, config: Dict[str, Any]) -> None:
        """Initialize Ollama provider.
        
        Args:
            config: Ollama configuration containing base_url and model
        """
        super().__init__(config)
        self.base_url = config.get("base_url", "http://localhost:11434")
        self.model = config.get("model", "llama2")
        
        self.client = httpx.AsyncClient(
            base_url=self.base_url,
            timeout=60.0,  # Longer timeout for local models
        )
    
    async def generate_response(
        self,
        messages: List[LLMMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stream: bool = False,
    ) -> LLMResponse:
        """Generate response from Ollama.
        
        Args:
            messages: Conversation messages
            tools: Available tools/functions (limited support)
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            stream: Whether to stream response
            
        Returns:
            LLM response object
        """
        try:
            # Convert messages to Ollama format
            prompt = self._messages_to_prompt(messages)
            
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                }
            }
            
            if max_tokens:
                payload["options"]["num_predict"] = max_tokens
            
            # Add tool information to prompt if provided
            if tools:
                tool_prompt = self._format_tools_for_prompt(tools)
                payload["prompt"] = f"{tool_prompt}\n\n{prompt}"
            
            response = await self.client.post("/api/generate", json=payload)
            response.raise_for_status()
            
            data = response.json()
            content = data.get("response", "")
            
            # Try to parse tool calls from response
            tool_calls = None
            if tools and content:
                tool_calls = self._parse_tool_calls_from_response(content)
            
            return LLMResponse(
                content=content,
                tool_calls=tool_calls,
                finish_reason="stop",
                usage=None,  # Ollama doesn't provide usage stats in this format
            )
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Ollama API error: {e.response.status_code}")
            raise LLMError(f"Ollama API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error calling Ollama: {e}")
            raise LLMError(f"Failed to call Ollama: {str(e)}")
    
    async def stream_response(
        self,
        messages: List[LLMMessage],
        tools: Optional[List[Dict[str, Any]]] = None,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
    ) -> AsyncGenerator[str, None]:
        """Stream response from Ollama.
        
        Args:
            messages: Conversation messages
            tools: Available tools/functions
            temperature: Sampling temperature
            max_tokens: Maximum tokens to generate
            
        Yields:
            Response chunks as they become available
        """
        try:
            prompt = self._messages_to_prompt(messages)
            
            payload = {
                "model": self.model,
                "prompt": prompt,
                "stream": True,
                "options": {
                    "temperature": temperature,
                }
            }
            
            if max_tokens:
                payload["options"]["num_predict"] = max_tokens
            
            if tools:
                tool_prompt = self._format_tools_for_prompt(tools)
                payload["prompt"] = f"{tool_prompt}\n\n{prompt}"
            
            async with self.client.stream("POST", "/api/generate", json=payload) as response:
                response.raise_for_status()
                
                async for line in response.aiter_lines():
                    if line.strip():
                        try:
                            data = json.loads(line)
                            if "response" in data and data["response"]:
                                yield data["response"]
                            
                            if data.get("done", False):
                                break
                                
                        except json.JSONDecodeError:
                            continue
                            
        except httpx.HTTPStatusError as e:
            logger.error(f"Ollama streaming error: {e.response.status_code}")
            raise LLMError(f"Ollama streaming error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error streaming from Ollama: {e}")
            raise LLMError(f"Failed to stream from Ollama: {str(e)}")
    
    async def is_available(self) -> bool:
        """Check if Ollama is available.
        
        Returns:
            True if Ollama server is running and model is available
        """
        try:
            # Check if server is running
            response = await self.client.get("/api/tags")
            if response.status_code != 200:
                return False
            
            # Check if our model is available
            models = response.json().get("models", [])
            model_names = [model.get("name", "") for model in models]
            
            return any(self.model in name for name in model_names)
            
        except Exception:
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get Ollama model information.
        
        Returns:
            Model information dictionary
        """
        return {
            "provider": "ollama",
            "model": self.model,
            "base_url": self.base_url,
            "supports_tools": True,  # Limited support via prompt engineering
            "supports_streaming": True,
        }
    
    def _messages_to_prompt(self, messages: List[LLMMessage]) -> str:
        """Convert messages to a single prompt string for Ollama.
        
        Args:
            messages: List of conversation messages
            
        Returns:
            Formatted prompt string
        """
        prompt_parts = []
        
        for message in messages:
            if message.role == "system":
                prompt_parts.append(f"System: {message.content}")
            elif message.role == "user":
                prompt_parts.append(f"User: {message.content}")
            elif message.role == "assistant":
                prompt_parts.append(f"Assistant: {message.content}")
            elif message.role == "tool":
                prompt_parts.append(f"Tool Result: {message.content}")
        
        return "\n\n".join(prompt_parts) + "\n\nAssistant:"
    
    def _format_tools_for_prompt(self, tools: List[Dict[str, Any]]) -> str:
        """Format tools as part of the system prompt.
        
        Args:
            tools: List of available tools
            
        Returns:
            Formatted tools description
        """
        tool_descriptions = []
        
        for tool in tools:
            name = tool.get("function", {}).get("name", "")
            description = tool.get("function", {}).get("description", "")
            parameters = tool.get("function", {}).get("parameters", {})
            
            tool_desc = f"Tool: {name}\nDescription: {description}\nParameters: {json.dumps(parameters, indent=2)}"
            tool_descriptions.append(tool_desc)
        
        tools_prompt = "Available Tools:\n" + "\n\n".join(tool_descriptions)
        tools_prompt += "\n\nTo use a tool, respond with a JSON object in this format:\n"
        tools_prompt += '{"tool_name": "function_name", "arguments": {"param1": "value1", "param2": "value2"}}'
        
        return tools_prompt
    
    def _parse_tool_calls_from_response(self, content: str) -> Optional[List[Dict[str, Any]]]:
        """Parse tool calls from Ollama response.
        
        Args:
            content: Response content from Ollama
            
        Returns:
            List of tool calls if found, None otherwise
        """
        try:
            # Look for JSON objects in the response
            import re
            json_pattern = r'\{[^{}]*"tool_name"[^{}]*\}'
            matches = re.findall(json_pattern, content)
            
            tool_calls = []
            for match in matches:
                try:
                    tool_data = json.loads(match)
                    if "tool_name" in tool_data and "arguments" in tool_data:
                        tool_call = {
                            "id": f"call_{len(tool_calls)}",
                            "type": "function",
                            "function": {
                                "name": tool_data["tool_name"],
                                "arguments": json.dumps(tool_data["arguments"])
                            }
                        }
                        tool_calls.append(tool_call)
                except json.JSONDecodeError:
                    continue
            
            return tool_calls if tool_calls else None
            
        except Exception:
            return None
    
    async def cleanup(self) -> None:
        """Clean up HTTP client."""
        await self.client.aclose()
