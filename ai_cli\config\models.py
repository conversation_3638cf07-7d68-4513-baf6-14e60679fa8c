"""Configuration data models."""

from dataclasses import dataclass, field
from pathlib import Path
from typing import Dict, List, Optional


@dataclass
class LLMConfig:
    """Configuration for LLM providers."""
    default_provider: str = "ollama"
    deepseek: Dict[str, str] = field(default_factory=lambda: {
        "api_key": "",
        "model": "deepseek-chat",
        "base_url": "https://api.deepseek.com/v1"
    })
    ollama: Dict[str, str] = field(default_factory=lambda: {
        "base_url": "http://localhost:11434",
        "model": "llama2"
    })


@dataclass
class UIConfig:
    """Configuration for UI behavior."""
    enable_animation: bool = True
    confirm_shell_commands: bool = True
    animation_speed: float = 0.1
    max_output_lines: int = 1000
    syntax_highlighting: bool = True
    show_timestamps: bool = True


@dataclass
class ContextConfig:
    """Configuration for context management."""
    auto_context: bool = True
    max_file_size: int = 1024 * 1024  # 1MB
    exclude_patterns: List[str] = field(default_factory=lambda: [
        "*.pyc", "*.pyo", "__pycache__", ".git", "node_modules",
        "*.log", "*.tmp", ".env", "*.key", "*.pem"
    ])
    include_git_info: bool = True
    max_context_files: int = 20


@dataclass
class SecurityConfig:
    """Configuration for security settings."""
    require_command_confirmation: bool = True
    allowed_commands: List[str] = field(default_factory=list)
    blocked_commands: List[str] = field(default_factory=lambda: [
        "rm -rf /", "sudo rm", "format", "del /s", "rmdir /s"
    ])
    max_command_length: int = 1000


@dataclass
class SessionConfig:
    """Configuration for session management."""
    auto_save: bool = True
    max_history_entries: int = 1000
    session_timeout: int = 3600  # 1 hour
    persist_context: bool = True


@dataclass
class WebSearchConfig:
    """Configuration for web search functionality."""
    enabled: bool = True
    provider: str = "duckduckgo"  # or "google", "bing"
    api_key: str = ""
    max_results: int = 5
    timeout: int = 10


@dataclass
class Config:
    """Main configuration class."""
    llm: LLMConfig = field(default_factory=LLMConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    context: ContextConfig = field(default_factory=ContextConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    session: SessionConfig = field(default_factory=SessionConfig)
    web_search: WebSearchConfig = field(default_factory=WebSearchConfig)
    
    # Paths
    config_dir: Optional[Path] = None
    log_dir: Optional[Path] = None
    session_dir: Optional[Path] = None
    
    def __post_init__(self) -> None:
        """Set default paths if not provided."""
        if self.config_dir is None:
            self.config_dir = Path.home() / ".config" / "ai_cli_tool"
        if self.log_dir is None:
            self.log_dir = self.config_dir / "logs"
        if self.session_dir is None:
            self.session_dir = self.config_dir / "sessions"
